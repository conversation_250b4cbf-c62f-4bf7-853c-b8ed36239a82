# Physics-Informed ML Configuration

# Data Configuration
data:
  dataset_path: "dataset/dielectric.csv"
  batch_size: 128
  train_split: 0.7
  val_split: 0.15
  test_split: 0.15
  num_workers: 4
  shuffle: true

# Model Configuration
model:
  # Physics-Informed NN
  pinn:
    hidden_dims: [256, 512, 256, 128]
    activation: "tanh"
    dropout_rate: 0.1
    physics_loss_weight: 1.0
    
  # Graph Neural Network
  gnn:
    node_features: 10
    edge_features: 5
    hidden_dim: 128
    num_layers: 4
    aggregation: "mean"
    
  # Transformer
  transformer:
    d_model: 256
    nhead: 8
    num_encoder_layers: 6
    dim_feedforward: 1024
    dropout: 0.1
    
  # Variational Autoencoder
  vae:
    latent_dim: 64
    encoder_dims: [256, 128]
    decoder_dims: [128, 256]
    beta: 1.0
    
  # Hybrid Model
  hybrid:
    fusion_dim: 512
    output_dim: 2  # [py1, py2] - matching the target dimensions
    uncertainty_heads: 5

# Training Configuration
training:
  epochs: 100
  learning_rate: 0.001
  weight_decay: 1e-5
  scheduler: "cosine"
  early_stopping_patience: 15
  gradient_clip_val: 1.0
  
  # Loss weights
  loss_weights:
    reconstruction: 1.0
    physics_constraint: 2.0
    anomaly_detection: 0.5
    uncertainty: 0.3

# Physics Constants
physics:
  # Conservation laws
  energy_conservation_tolerance: 1e-3
  momentum_conservation_tolerance: 1e-3
  charge_conservation_tolerance: 1e-6
  
  # Particle properties
  electron_mass: 0.511  # MeV/c²
  speed_of_light: 299792458  # m/s
  elementary_charge: 1.602176634e-19  # C

# Inference Configuration
inference:
  batch_size: 1
  uncertainty_samples: 100
  anomaly_threshold: 0.95
  real_time_buffer_size: 1000

# Visualization Configuration
visualization:
  plot_style: "seaborn-v0_8"
  figure_size: [12, 8]
  dpi: 300
  save_format: "png"
  
  # 3D visualization
  particle_size: 0.1
  trajectory_alpha: 0.7
  animation_fps: 30

# Logging Configuration
logging:
  level: "INFO"
  log_dir: "logs"
  wandb_project: "physics-discovery"
  save_plots: true
  plot_interval: 10

# Hardware Configuration
hardware:
  device: "auto"  # auto, cpu, cuda
  mixed_precision: true
  compile_model: true
  
# Paths
paths:
  models_dir: "models"
  plots_dir: "plots"
  logs_dir: "logs"
  frontend_dir: "frontend"
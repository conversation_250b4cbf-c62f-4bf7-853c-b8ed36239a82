{"version": 3, "file": "4971.e850b0a1dcb6d3fce7a4.js?v=e850b0a1dcb6d3fce7a4", "mappings": ";;;;;;AAAa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,eAAe;AACf;;;;;;;ACJa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,2BAA2B,mBAAO,CAAC,KAA4B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D,UAAU;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;ACpEa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,eAAe;AACf,mBAAmB,mBAAO,CAAC,KAAyB;AACpD,uBAAuB,mBAAO,CAAC,KAAuB;AACtD,mBAAmB,mBAAO,CAAC,KAAmB;AAC9C,eAAe;AACf;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;;;;;;AChBa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA,UAAU;AACV,sCAAsC,gCAAgC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;ACvCa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,GAAG,wBAAwB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,uBAAuB;AACpE,6CAA6C,oBAAoB;AACjE;AACA;AACA,qDAAqD,uBAAuB;AAC5E;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/components/version.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/HandlerList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/mathjax.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/PrioritizedList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/Retries.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.VERSION = void 0;\nexports.VERSION = '3.2.2';\n//# sourceMappingURL=version.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HandlerList = void 0;\nvar PrioritizedList_js_1 = require(\"../util/PrioritizedList.js\");\nvar HandlerList = (function (_super) {\n    __extends(HandlerList, _super);\n    function HandlerList() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    HandlerList.prototype.register = function (handler) {\n        return this.add(handler, handler.priority);\n    };\n    HandlerList.prototype.unregister = function (handler) {\n        this.remove(handler);\n    };\n    HandlerList.prototype.handlesDocument = function (document) {\n        var e_1, _a;\n        try {\n            for (var _b = __values(this), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                var handler = item.item;\n                if (handler.handlesDocument(document)) {\n                    return handler;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        throw new Error(\"Can't find handler for document\");\n    };\n    HandlerList.prototype.document = function (document, options) {\n        if (options === void 0) { options = null; }\n        return this.handlesDocument(document).create(document, options);\n    };\n    return HandlerList;\n}(PrioritizedList_js_1.PrioritizedList));\nexports.HandlerList = HandlerList;\n//# sourceMappingURL=HandlerList.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.mathjax = void 0;\nvar version_js_1 = require(\"./components/version.js\");\nvar HandlerList_js_1 = require(\"./core/HandlerList.js\");\nvar Retries_js_1 = require(\"./util/Retries.js\");\nexports.mathjax = {\n    version: version_js_1.VERSION,\n    handlers: new HandlerList_js_1.HandlerList(),\n    document: function (document, options) {\n        return exports.mathjax.handlers.document(document, options);\n    },\n    handleRetriesFor: Retries_js_1.handleRetriesFor,\n    retryAfter: Retries_js_1.retryAfter,\n    asyncLoad: null,\n};\n//# sourceMappingURL=mathjax.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PrioritizedList = void 0;\nvar PrioritizedList = (function () {\n    function PrioritizedList() {\n        this.items = [];\n        this.items = [];\n    }\n    PrioritizedList.prototype[Symbol.iterator] = function () {\n        var i = 0;\n        var items = this.items;\n        return {\n            next: function () {\n                return { value: items[i++], done: (i > items.length) };\n            }\n        };\n    };\n    PrioritizedList.prototype.add = function (item, priority) {\n        if (priority === void 0) { priority = PrioritizedList.DEFAULTPRIORITY; }\n        var i = this.items.length;\n        do {\n            i--;\n        } while (i >= 0 && priority < this.items[i].priority);\n        this.items.splice(i + 1, 0, { item: item, priority: priority });\n        return item;\n    };\n    PrioritizedList.prototype.remove = function (item) {\n        var i = this.items.length;\n        do {\n            i--;\n        } while (i >= 0 && this.items[i].item !== item);\n        if (i >= 0) {\n            this.items.splice(i, 1);\n        }\n    };\n    PrioritizedList.DEFAULTPRIORITY = 5;\n    return PrioritizedList;\n}());\nexports.PrioritizedList = PrioritizedList;\n//# sourceMappingURL=PrioritizedList.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.retryAfter = exports.handleRetriesFor = void 0;\nfunction handleRetriesFor(code) {\n    return new Promise(function run(ok, fail) {\n        try {\n            ok(code());\n        }\n        catch (err) {\n            if (err.retry && err.retry instanceof Promise) {\n                err.retry.then(function () { return run(ok, fail); })\n                    .catch(function (perr) { return fail(perr); });\n            }\n            else if (err.restart && err.restart.isCallback) {\n                MathJax.Callback.After(function () { return run(ok, fail); }, err.restart);\n            }\n            else {\n                fail(err);\n            }\n        }\n    });\n}\nexports.handleRetriesFor = handleRetriesFor;\nfunction retryAfter(promise) {\n    var err = new Error('MathJax retry');\n    err.retry = promise;\n    throw err;\n}\nexports.retryAfter = retryAfter;\n//# sourceMappingURL=Retries.js.map"], "names": [], "sourceRoot": ""}
{"name": "@jupyterlab/mathjax-extension", "version": "4.4.4", "description": "A JupyterLab extension providing MathJax Typesetting", "keywords": ["jup<PERSON><PERSON>", "jupyterlab", "mathjax"], "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "Project Jupyter", "email": "<EMAIL>"}, "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "schema/*.json", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "eslint": "eslint . --ext .ts,.tsx --fix", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.4.4", "@jupyterlab/rendermime": "^4.4.4", "@lumino/coreutils": "^2.2.1", "mathjax-full": "^3.2.2"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}
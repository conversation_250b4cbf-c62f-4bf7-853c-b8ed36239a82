((= Black&white Python input/output style =))

((*- extends 'base.tex.j2' -*))

%===============================================================================
% Input
%===============================================================================

((* block input scoped *))
\begin{verbatim}
((*- if resources.global_content_filter.include_input_prompt *))
((( cell.source | add_prompts )))
((* else *))
((( cell.source )))
((* endif *))
\end{verbatim}
((* endblock input *))

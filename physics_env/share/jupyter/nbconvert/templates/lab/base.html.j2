{%- extends 'display_priority.j2' -%}
{% from 'celltags.j2' import celltags %}
{% from 'cell_id_anchor.j2' import cell_id_anchor %}

{% block codecell %}
{%- if not cell.outputs -%}
{%- set no_output_class="jp-mod-noOutputs" -%}
{%- endif -%}
{%- if not resources.global_content_filter.include_input -%}
{%- set no_input_class="jp-mod-noInput" -%}
{%- endif -%}
<div {{ cell_id_anchor(cell) }} class="jp-Cell jp-CodeCell jp-Notebook-cell {{ no_output_class }} {{ no_input_class }} {{ celltags(cell) }}">
{{ super() }}
</div>
{%- endblock codecell %}

{% block input_group -%}
<div class="jp-Cell-inputWrapper">
<div class="jp-Collapser jp-InputCollapser jp-Cell-inputCollapser">
</div>
<div class="jp-InputArea jp-Cell-inputArea">
{{ super() }}
</div>
</div>
{% endblock input_group %}

{% block input %}
<div class="jp-CodeMirrorEditor jp-Editor jp-InputArea-editor" data-type="inline">
     <div class="cm-editor cm-s-jupyter">
{{ cell.source | highlight_code(metadata=cell.metadata) | clean_html }}
     </div>
</div>
{%- endblock input %}

{% block output_group %}
<div class="jp-Cell-outputWrapper">
<div class="jp-Collapser jp-OutputCollapser jp-Cell-outputCollapser">
</div>
{{ super() }}
</div>
{% endblock output_group %}

{% block outputs %}
<div class="jp-OutputArea jp-Cell-outputArea">
{{ super() }}
</div>
{% endblock outputs %}

{% block in_prompt -%}
<div class="jp-InputPrompt jp-InputArea-prompt">
    {%- if cell.execution_count is defined -%}
        In&nbsp;[{{ cell.execution_count|replace(None, "&nbsp;") }}]:
    {%- else -%}
        In&nbsp;[&nbsp;]:
    {%- endif -%}
</div>
{%- endblock in_prompt %}

{% block empty_in_prompt -%}
<div class="jp-InputPrompt jp-InputArea-prompt">
</div>
{%- endblock empty_in_prompt %}

{#
  output_prompt doesn't do anything in HTML,
  because there is a prompt div in each output area (see output block)
 #}
{% block output_prompt %}
{% endblock output_prompt %}

{% block output_area_prompt %}
    <div class="jp-OutputPrompt jp-OutputArea-prompt">
{%- if output.output_type == 'execute_result' -%}
    {%- if cell.execution_count is defined -%}
        Out[{{ cell.execution_count|replace(None, "&nbsp;") }}]:
    {%- else -%}
        Out[&nbsp;]:
    {%- endif -%}
{%- endif -%}
    </div>
{% endblock output_area_prompt %}

{% block output %}
{%- if output.output_type == 'execute_result' -%}
<div class="jp-OutputArea-child jp-OutputArea-executeResult">
{%- else -%}
<div class="jp-OutputArea-child">
{%- endif -%}
{% if resources.global_content_filter.include_output_prompt %}
    {{ self.output_area_prompt() }}
{% endif %}
{{ super() }}
</div>
{% endblock output %}

{% block markdowncell scoped %}
<div {{ cell_id_anchor(cell) }} class="jp-Cell jp-MarkdownCell jp-Notebook-cell">
<div class="jp-Cell-inputWrapper">
<div class="jp-Collapser jp-InputCollapser jp-Cell-inputCollapser">
</div>
<div class="jp-InputArea jp-Cell-inputArea">
{%- if resources.global_content_filter.include_input_prompt-%}
    {{ self.empty_in_prompt() }}
{%- endif -%}
<div class="jp-RenderedHTMLCommon jp-RenderedMarkdown jp-MarkdownOutput {{ celltags(cell) }}" data-mime-type="text/markdown">
{%- if resources.should_sanitize_html %}
{%- set html_value=cell.source  | markdown2html | strip_files_prefix | clean_html -%}
{%- else %}
{%- set html_value=cell.source  | markdown2html | strip_files_prefix -%}
{%- endif %}
{{ html_value }}
</div>
</div>
</div>
</div>
{%- endblock markdowncell %}

{% block rawcell scoped %}
{%- if cell.metadata.get('raw_mimetype', '').lower() in resources.get('raw_mimetypes', ['']) -%}
{{ cell.source | clean_html }}
{%- endif -%}
{%- endblock rawcell %}

{% block unknowncell scoped %}
unknown type  {{ cell.type }}
{% endblock unknowncell %}

{% block execute_result -%}
{%- set extra_class="jp-OutputArea-executeResult" -%}
{% block data_priority scoped %}
{{ super() }}
{% endblock data_priority %}
{%- set extra_class="" -%}
{%- endblock execute_result %}

{% block stream_stdout -%}
<div class="jp-RenderedText jp-OutputArea-output" data-mime-type="text/plain">
<pre>
{{- output.text | ansi2html -}}
</pre>
</div>
{%- endblock stream_stdout %}

{% block stream_stderr -%}
<div class="jp-RenderedText jp-OutputArea-output" data-mime-type="application/vnd.jupyter.stderr">
<pre>
{{- output.text | ansi2html -}}
</pre>
</div>
{%- endblock stream_stderr %}

{% block stream_stdin -%}
{%- if resources.global_content_filter.include_output_stdin -%}
<div class="jp-RenderedText jp-OutputArea-output" data-mime-type="application/vnd.jupyter.stdin">
<pre>
{{- output.text | ansi2html -}}
</pre>
</div>
{%- endif %}
{%- endblock stream_stdin %}

{% block data_svg scoped -%}
<div class="jp-RenderedSVG jp-OutputArea-output {{ extra_class }}" data-mime-type="image/svg+xml">
{%- if output.svg_filename %}
<img src="{{ output.svg_filename | posix_path | escape_html }}">
{%- else %}
    {%- if resources.should_not_encode_svg %}
        {{ output.data['image/svg+xml'].encode("utf-8") | clean_html }}
    {%- else %}
        <img src="data:image/svg+xml;base64,{{ output.data['image/svg+xml'] | text_base64 | escape_html }}">
    {%- endif %}
{%- endif %}
</div>
{%- endblock data_svg %}

{% block data_mermaid scoped -%}
<div class="jp-Mermaid">
<pre class="mermaid">
{{ output.data['text/vnd.mermaid'].strip() }}
</pre>
</div>
{%- endblock data_mermaid %}

{% block data_html scoped -%}
<div class="jp-RenderedHTMLCommon jp-RenderedHTML jp-OutputArea-output {{ extra_class }}" data-mime-type="text/html">
{%- if resources.should_sanitize_html %}
{{ output.data['text/html'] | clean_html }}
{%- else %}
{{ output.data['text/html'] }}
{%- endif %}
</div>
{%- endblock data_html %}

{% block data_markdown scoped -%}
{%- if resources.should_sanitize_html %}
{%- set html_value=output.data['text/markdown'] | markdown2html | clean_html -%}
{%- else %}
{%- set html_value=output.data['text/markdown'] | markdown2html -%}
{%- endif %}
<div class="jp-RenderedHTMLCommon jp-RenderedMarkdown jp-OutputArea-output {{ extra_class }}" data-mime-type="text/markdown">
{{ html_value }}
</div>
{%- endblock data_markdown %}

{% block data_png scoped %}
<div class="jp-RenderedImage jp-OutputArea-output {{ extra_class }}">
{%- if 'image/png' in output.metadata.get('filenames', {}) %}
<img src="{{ output.metadata.filenames['image/png'] | posix_path | escape_html }}"
{%- else %}
<img src="data:image/png;base64,{{ output.data['image/png'] | escape_html }}"
{%- endif %}
{%- set width=output | get_metadata('width', 'image/png') -%}
{%- if width is not none %}
width={{ width | escape_html }}
{%- endif %}
{%- set height=output | get_metadata('height', 'image/png') -%}
{%- if height is not none %}
height={{ height | escape_html }}
{%- endif %}
class="
{%- if output | get_metadata('unconfined', 'image/png') %}
unconfined
{%- endif %}
{%- if output | get_metadata('needs_background', 'image/png') == 'light' %}
jp-needs-light-background
{%- endif %}
{%- if output | get_metadata('needs_background', 'image/png') == 'dark' %}
jp-needs-dark-background
{%- endif %}
"
>
</div>
{%- endblock data_png %}

{% block data_jpg scoped %}
<div class="jp-RenderedImage jp-OutputArea-output {{ extra_class }}">
{%- if 'image/jpeg' in output.metadata.get('filenames', {}) %}
<img src="{{ output.metadata.filenames['image/jpeg'] | posix_path | escape_html }}"
{%- else %}
<img src="data:image/jpeg;base64,{{ output.data['image/jpeg'] | escape_html }}"
{%- endif %}
{%- set width=output | get_metadata('width', 'image/jpeg') -%}
{%- if width is not none %}
width={{ width | escape_html }}
{%- endif %}
{%- set height=output | get_metadata('height', 'image/jpeg') -%}
{%- if height is not none %}
height={{ height | escape_html }}
{%- endif %}
class="
{%- if output | get_metadata('unconfined', 'image/jpeg') %}
unconfined
{%- endif %}
{%- if output | get_metadata('needs_background', 'image/jpeg') == 'light' %}
jp-needs-light-background
{%- endif %}
{%- if output | get_metadata('needs_background', 'image/jpeg') == 'dark' %}
jp-needs-dark-background
{%- endif %}
"
>
</div>
{%- endblock data_jpg %}

{% block data_latex scoped %}
<div class="jp-RenderedLatex jp-OutputArea-output {{ extra_class }}" data-mime-type="text/latex">
{{ output.data['text/latex'] | e }}
</div>
{%- endblock data_latex %}

{% block error -%}
<div class="jp-RenderedText jp-OutputArea-output" data-mime-type="application/vnd.jupyter.stderr">
<pre>
{{- super() -}}
</pre>
</div>
{%- endblock error %}

{%- block traceback_line %}
{{ line | ansi2html }}
{%- endblock traceback_line %}

{%- block data_text scoped %}
<div class="jp-RenderedText jp-OutputArea-output {{ extra_class }}" data-mime-type="text/plain">
<pre>
{{- output.data['text/plain'] | ansi2html -}}
</pre>
</div>
{%- endblock -%}

{#
 ###############################################################################
 # TODO: how to better handle JavaScript repr?                                 #
 ###############################################################################
 #}

{%- block data_javascript scoped %}
{% set div_id = uuid4() %}
<div id="{{ div_id }}" class="jp-RenderedJavaScript jp-OutputArea-output {{ extra_class }}" data-mime-type="application/javascript">
{%- if not resources.should_sanitize_html %}
<script type="text/javascript">
var element = document.getElementById('{{ div_id }}');
{{ output.data['application/javascript'] }}
</script>
{%- endif %}
</div>
{%- endblock -%}

{%- block data_widget_view scoped %}
{% set div_id = uuid4() %}
{% set datatype_list = output.data | filter_data_type %}
{% set datatype = datatype_list[0]%}
<div id="{{ div_id }}" class="jupyter-widgets jp-OutputArea-output {{ extra_class }}">
<script type="text/javascript">
var element = document.getElementById('{{ div_id }}');
</script>
<script type="{{ datatype }}">
{{ output.data[datatype] | json_dumps | escape_html_script }}
</script>
</div>
{%- endblock data_widget_view -%}

{%- block footer %}
{% set mimetype = 'application/vnd.jupyter.widget-state+json'%}
{% if mimetype in nb.metadata.get("widgets",{})%}
<script type="{{ mimetype }}">
{{ nb.metadata.widgets[mimetype] | json_dumps | escape_html_script }}
</script>
{% endif %}
{{ super() }}
{%- endblock footer-%}

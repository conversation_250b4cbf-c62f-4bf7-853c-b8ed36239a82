"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[3282],{63282:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=t.Ajv=void 0;const s=r(4042);const n=r(86144);const o=r(36653);const a=r(72079);const i=["/properties"];const c="http://json-schema.org/draft-07/schema";class u extends s.default{_addVocabularies(){super._addVocabularies();n.default.forEach((e=>this.addVocabulary(e)));if(this.opts.discriminator)this.addKeyword(o.default)}_addDefaultMetaSchema(){super._addDefaultMetaSchema();if(!this.opts.meta)return;const e=this.opts.$data?this.$dataMetaSchema(a,i):a;this.addMetaSchema(e,c,false);this.refs["http://json-schema.org/schema"]=c}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(c)?c:undefined)}}t.Ajv=u;e.exports=t=u;e.exports.Ajv=u;Object.defineProperty(t,"__esModule",{value:true});t["default"]=u;var d=r(62586);Object.defineProperty(t,"KeywordCxt",{enumerable:true,get:function(){return d.KeywordCxt}});var l=r(99029);Object.defineProperty(t,"_",{enumerable:true,get:function(){return l._}});Object.defineProperty(t,"str",{enumerable:true,get:function(){return l.str}});Object.defineProperty(t,"stringify",{enumerable:true,get:function(){return l.stringify}});Object.defineProperty(t,"nil",{enumerable:true,get:function(){return l.nil}});Object.defineProperty(t,"Name",{enumerable:true,get:function(){return l.Name}});Object.defineProperty(t,"CodeGen",{enumerable:true,get:function(){return l.CodeGen}});var f=r(13558);Object.defineProperty(t,"ValidationError",{enumerable:true,get:function(){return f.default}});var h=r(34551);Object.defineProperty(t,"MissingRefError",{enumerable:true,get:function(){return h.default}})},41520:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});t.regexpCode=t.getEsmExportName=t.getProperty=t.safeStringify=t.stringify=t.strConcat=t.addCodeArg=t.str=t._=t.nil=t._Code=t.Name=t.IDENTIFIER=t._CodeOrName=void 0;class r{}t._CodeOrName=r;t.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class s extends r{constructor(e){super();if(!t.IDENTIFIER.test(e))throw new Error("CodeGen: name must be a valid identifier");this.str=e}toString(){return this.str}emptyStr(){return false}get names(){return{[this.str]:1}}}t.Name=s;class n extends r{constructor(e){super();this._items=typeof e==="string"?[e]:e}toString(){return this.str}emptyStr(){if(this._items.length>1)return false;const e=this._items[0];return e===""||e==='""'}get str(){var e;return(e=this._str)!==null&&e!==void 0?e:this._str=this._items.reduce(((e,t)=>`${e}${t}`),"")}get names(){var e;return(e=this._names)!==null&&e!==void 0?e:this._names=this._items.reduce(((e,t)=>{if(t instanceof s)e[t.str]=(e[t.str]||0)+1;return e}),{})}}t._Code=n;t.nil=new n("");function o(e,...t){const r=[e[0]];let s=0;while(s<t.length){c(r,t[s]);r.push(e[++s])}return new n(r)}t._=o;const a=new n("+");function i(e,...t){const r=[p(e[0])];let s=0;while(s<t.length){r.push(a);c(r,t[s]);r.push(a,p(e[++s]))}u(r);return new n(r)}t.str=i;function c(e,t){if(t instanceof n)e.push(...t._items);else if(t instanceof s)e.push(t);else e.push(f(t))}t.addCodeArg=c;function u(e){let t=1;while(t<e.length-1){if(e[t]===a){const r=d(e[t-1],e[t+1]);if(r!==undefined){e.splice(t-1,3,r);continue}e[t++]="+"}t++}}function d(e,t){if(t==='""')return e;if(e==='""')return t;if(typeof e=="string"){if(t instanceof s||e[e.length-1]!=='"')return;if(typeof t!="string")return`${e.slice(0,-1)}${t}"`;if(t[0]==='"')return e.slice(0,-1)+t.slice(1);return}if(typeof t=="string"&&t[0]==='"'&&!(e instanceof s))return`"${e}${t.slice(1)}`;return}function l(e,t){return t.emptyStr()?e:e.emptyStr()?t:i`${e}${t}`}t.strConcat=l;function f(e){return typeof e=="number"||typeof e=="boolean"||e===null?e:p(Array.isArray(e)?e.join(","):e)}function h(e){return new n(p(e))}t.stringify=h;function p(e){return JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}t.safeStringify=p;function m(e){return typeof e=="string"&&t.IDENTIFIER.test(e)?new n(`.${e}`):o`[${e}]`}t.getProperty=m;function y(e){if(typeof e=="string"&&t.IDENTIFIER.test(e)){return new n(`${e}`)}throw new Error(`CodeGen: invalid export name: ${e}, use explicit $id name mapping`)}t.getEsmExportName=y;function g(e){return new n(e.toString())}t.regexpCode=g},99029:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.or=t.and=t.not=t.CodeGen=t.operators=t.varKinds=t.ValueScopeName=t.ValueScope=t.Scope=t.Name=t.regexpCode=t.stringify=t.getProperty=t.nil=t.strConcat=t.str=t._=void 0;const s=r(41520);const n=r(57845);var o=r(41520);Object.defineProperty(t,"_",{enumerable:true,get:function(){return o._}});Object.defineProperty(t,"str",{enumerable:true,get:function(){return o.str}});Object.defineProperty(t,"strConcat",{enumerable:true,get:function(){return o.strConcat}});Object.defineProperty(t,"nil",{enumerable:true,get:function(){return o.nil}});Object.defineProperty(t,"getProperty",{enumerable:true,get:function(){return o.getProperty}});Object.defineProperty(t,"stringify",{enumerable:true,get:function(){return o.stringify}});Object.defineProperty(t,"regexpCode",{enumerable:true,get:function(){return o.regexpCode}});Object.defineProperty(t,"Name",{enumerable:true,get:function(){return o.Name}});var a=r(57845);Object.defineProperty(t,"Scope",{enumerable:true,get:function(){return a.Scope}});Object.defineProperty(t,"ValueScope",{enumerable:true,get:function(){return a.ValueScope}});Object.defineProperty(t,"ValueScopeName",{enumerable:true,get:function(){return a.ValueScopeName}});Object.defineProperty(t,"varKinds",{enumerable:true,get:function(){return a.varKinds}});t.operators={GT:new s._Code(">"),GTE:new s._Code(">="),LT:new s._Code("<"),LTE:new s._Code("<="),EQ:new s._Code("==="),NEQ:new s._Code("!=="),NOT:new s._Code("!"),OR:new s._Code("||"),AND:new s._Code("&&"),ADD:new s._Code("+")};class i{optimizeNodes(){return this}optimizeNames(e,t){return this}}class c extends i{constructor(e,t,r){super();this.varKind=e;this.name=t;this.rhs=r}render({es5:e,_n:t}){const r=e?n.varKinds.var:this.varKind;const s=this.rhs===undefined?"":` = ${this.rhs}`;return`${r} ${this.name}${s};`+t}optimizeNames(e,t){if(!e[this.name.str])return;if(this.rhs)this.rhs=x(this.rhs,e,t);return this}get names(){return this.rhs instanceof s._CodeOrName?this.rhs.names:{}}}class u extends i{constructor(e,t,r){super();this.lhs=e;this.rhs=t;this.sideEffects=r}render({_n:e}){return`${this.lhs} = ${this.rhs};`+e}optimizeNames(e,t){if(this.lhs instanceof s.Name&&!e[this.lhs.str]&&!this.sideEffects)return;this.rhs=x(this.rhs,e,t);return this}get names(){const e=this.lhs instanceof s.Name?{}:{...this.lhs.names};return I(e,this.rhs)}}class d extends u{constructor(e,t,r,s){super(e,r,s);this.op=t}render({_n:e}){return`${this.lhs} ${this.op}= ${this.rhs};`+e}}class l extends i{constructor(e){super();this.label=e;this.names={}}render({_n:e}){return`${this.label}:`+e}}class f extends i{constructor(e){super();this.label=e;this.names={}}render({_n:e}){const t=this.label?` ${this.label}`:"";return`break${t};`+e}}class h extends i{constructor(e){super();this.error=e}render({_n:e}){return`throw ${this.error};`+e}get names(){return this.error.names}}class p extends i{constructor(e){super();this.code=e}render({_n:e}){return`${this.code};`+e}optimizeNodes(){return`${this.code}`?this:undefined}optimizeNames(e,t){this.code=x(this.code,e,t);return this}get names(){return this.code instanceof s._CodeOrName?this.code.names:{}}}class m extends i{constructor(e=[]){super();this.nodes=e}render(e){return this.nodes.reduce(((t,r)=>t+r.render(e)),"")}optimizeNodes(){const{nodes:e}=this;let t=e.length;while(t--){const r=e[t].optimizeNodes();if(Array.isArray(r))e.splice(t,1,...r);else if(r)e[t]=r;else e.splice(t,1)}return e.length>0?this:undefined}optimizeNames(e,t){const{nodes:r}=this;let s=r.length;while(s--){const n=r[s];if(n.optimizeNames(e,t))continue;T(e,n.names);r.splice(s,1)}return r.length>0?this:undefined}get names(){return this.nodes.reduce(((e,t)=>C(e,t.names)),{})}}class y extends m{render(e){return"{"+e._n+super.render(e)+"}"+e._n}}class g extends m{}class $ extends y{}$.kind="else";class v extends y{constructor(e,t){super(t);this.condition=e}render(e){let t=`if(${this.condition})`+super.render(e);if(this.else)t+="else "+this.else.render(e);return t}optimizeNodes(){super.optimizeNodes();const e=this.condition;if(e===true)return this.nodes;let t=this.else;if(t){const e=t.optimizeNodes();t=this.else=Array.isArray(e)?new $(e):e}if(t){if(e===false)return t instanceof v?t:t.nodes;if(this.nodes.length)return this;return new v(R(e),t instanceof v?[t]:t.nodes)}if(e===false||!this.nodes.length)return undefined;return this}optimizeNames(e,t){var r;this.else=(r=this.else)===null||r===void 0?void 0:r.optimizeNames(e,t);if(!(super.optimizeNames(e,t)||this.else))return;this.condition=x(this.condition,e,t);return this}get names(){const e=super.names;I(e,this.condition);if(this.else)C(e,this.else.names);return e}}v.kind="if";class _ extends y{}_.kind="for";class w extends _{constructor(e){super();this.iteration=e}render(e){return`for(${this.iteration})`+super.render(e)}optimizeNames(e,t){if(!super.optimizeNames(e,t))return;this.iteration=x(this.iteration,e,t);return this}get names(){return C(super.names,this.iteration.names)}}class b extends _{constructor(e,t,r,s){super();this.varKind=e;this.name=t;this.from=r;this.to=s}render(e){const t=e.es5?n.varKinds.var:this.varKind;const{name:r,from:s,to:o}=this;return`for(${t} ${r}=${s}; ${r}<${o}; ${r}++)`+super.render(e)}get names(){const e=I(super.names,this.from);return I(e,this.to)}}class P extends _{constructor(e,t,r,s){super();this.loop=e;this.varKind=t;this.name=r;this.iterable=s}render(e){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(e)}optimizeNames(e,t){if(!super.optimizeNames(e,t))return;this.iterable=x(this.iterable,e,t);return this}get names(){return C(super.names,this.iterable.names)}}class E extends y{constructor(e,t,r){super();this.name=e;this.args=t;this.async=r}render(e){const t=this.async?"async ":"";return`${t}function ${this.name}(${this.args})`+super.render(e)}}E.kind="func";class S extends m{render(e){return"return "+super.render(e)}}S.kind="return";class k extends y{render(e){let t="try"+super.render(e);if(this.catch)t+=this.catch.render(e);if(this.finally)t+=this.finally.render(e);return t}optimizeNodes(){var e,t;super.optimizeNodes();(e=this.catch)===null||e===void 0?void 0:e.optimizeNodes();(t=this.finally)===null||t===void 0?void 0:t.optimizeNodes();return this}optimizeNames(e,t){var r,s;super.optimizeNames(e,t);(r=this.catch)===null||r===void 0?void 0:r.optimizeNames(e,t);(s=this.finally)===null||s===void 0?void 0:s.optimizeNames(e,t);return this}get names(){const e=super.names;if(this.catch)C(e,this.catch.names);if(this.finally)C(e,this.finally.names);return e}}class N extends y{constructor(e){super();this.error=e}render(e){return`catch(${this.error})`+super.render(e)}}N.kind="catch";class j extends y{render(e){return"finally"+super.render(e)}}j.kind="finally";class O{constructor(e,t={}){this._values={};this._blockStarts=[];this._constants={};this.opts={...t,_n:t.lines?"\n":""};this._extScope=e;this._scope=new n.Scope({parent:e});this._nodes=[new g]}toString(){return this._root.render(this.opts)}name(e){return this._scope.name(e)}scopeName(e){return this._extScope.name(e)}scopeValue(e,t){const r=this._extScope.value(e,t);const s=this._values[r.prefix]||(this._values[r.prefix]=new Set);s.add(r);return r}getScopeValue(e,t){return this._extScope.getValue(e,t)}scopeRefs(e){return this._extScope.scopeRefs(e,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(e,t,r,s){const n=this._scope.toName(t);if(r!==undefined&&s)this._constants[n.str]=r;this._leafNode(new c(e,n,r));return n}const(e,t,r){return this._def(n.varKinds.const,e,t,r)}let(e,t,r){return this._def(n.varKinds.let,e,t,r)}var(e,t,r){return this._def(n.varKinds.var,e,t,r)}assign(e,t,r){return this._leafNode(new u(e,t,r))}add(e,r){return this._leafNode(new d(e,t.operators.ADD,r))}code(e){if(typeof e=="function")e();else if(e!==s.nil)this._leafNode(new p(e));return this}object(...e){const t=["{"];for(const[r,n]of e){if(t.length>1)t.push(",");t.push(r);if(r!==n||this.opts.es5){t.push(":");(0,s.addCodeArg)(t,n)}}t.push("}");return new s._Code(t)}if(e,t,r){this._blockNode(new v(e));if(t&&r){this.code(t).else().code(r).endIf()}else if(t){this.code(t).endIf()}else if(r){throw new Error('CodeGen: "else" body without "then" body')}return this}elseIf(e){return this._elseNode(new v(e))}else(){return this._elseNode(new $)}endIf(){return this._endBlockNode(v,$)}_for(e,t){this._blockNode(e);if(t)this.code(t).endFor();return this}for(e,t){return this._for(new w(e),t)}forRange(e,t,r,s,o=(this.opts.es5?n.varKinds.var:n.varKinds.let)){const a=this._scope.toName(e);return this._for(new b(o,a,t,r),(()=>s(a)))}forOf(e,t,r,o=n.varKinds.const){const a=this._scope.toName(e);if(this.opts.es5){const e=t instanceof s.Name?t:this.var("_arr",t);return this.forRange("_i",0,(0,s._)`${e}.length`,(t=>{this.var(a,(0,s._)`${e}[${t}]`);r(a)}))}return this._for(new P("of",o,a,t),(()=>r(a)))}forIn(e,t,r,o=(this.opts.es5?n.varKinds.var:n.varKinds.const)){if(this.opts.ownProperties){return this.forOf(e,(0,s._)`Object.keys(${t})`,r)}const a=this._scope.toName(e);return this._for(new P("in",o,a,t),(()=>r(a)))}endFor(){return this._endBlockNode(_)}label(e){return this._leafNode(new l(e))}break(e){return this._leafNode(new f(e))}return(e){const t=new S;this._blockNode(t);this.code(e);if(t.nodes.length!==1)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(S)}try(e,t,r){if(!t&&!r)throw new Error('CodeGen: "try" without "catch" and "finally"');const s=new k;this._blockNode(s);this.code(e);if(t){const e=this.name("e");this._currNode=s.catch=new N(e);t(e)}if(r){this._currNode=s.finally=new j;this.code(r)}return this._endBlockNode(N,j)}throw(e){return this._leafNode(new h(e))}block(e,t){this._blockStarts.push(this._nodes.length);if(e)this.code(e).endBlock(t);return this}endBlock(e){const t=this._blockStarts.pop();if(t===undefined)throw new Error("CodeGen: not in self-balancing block");const r=this._nodes.length-t;if(r<0||e!==undefined&&r!==e){throw new Error(`CodeGen: wrong number of nodes: ${r} vs ${e} expected`)}this._nodes.length=t;return this}func(e,t=s.nil,r,n){this._blockNode(new E(e,t,r));if(n)this.code(n).endFunc();return this}endFunc(){return this._endBlockNode(E)}optimize(e=1){while(e-- >0){this._root.optimizeNodes();this._root.optimizeNames(this._root.names,this._constants)}}_leafNode(e){this._currNode.nodes.push(e);return this}_blockNode(e){this._currNode.nodes.push(e);this._nodes.push(e)}_endBlockNode(e,t){const r=this._currNode;if(r instanceof e||t&&r instanceof t){this._nodes.pop();return this}throw new Error(`CodeGen: not in block "${t?`${e.kind}/${t.kind}`:e.kind}"`)}_elseNode(e){const t=this._currNode;if(!(t instanceof v)){throw new Error('CodeGen: "else" without "if"')}this._currNode=t.else=e;return this}get _root(){return this._nodes[0]}get _currNode(){const e=this._nodes;return e[e.length-1]}set _currNode(e){const t=this._nodes;t[t.length-1]=e}}t.CodeGen=O;function C(e,t){for(const r in t)e[r]=(e[r]||0)+(t[r]||0);return e}function I(e,t){return t instanceof s._CodeOrName?C(e,t.names):e}function x(e,t,r){if(e instanceof s.Name)return n(e);if(!o(e))return e;return new s._Code(e._items.reduce(((e,t)=>{if(t instanceof s.Name)t=n(t);if(t instanceof s._Code)e.push(...t._items);else e.push(t);return e}),[]));function n(e){const s=r[e.str];if(s===undefined||t[e.str]!==1)return e;delete t[e.str];return s}function o(e){return e instanceof s._Code&&e._items.some((e=>e instanceof s.Name&&t[e.str]===1&&r[e.str]!==undefined))}}function T(e,t){for(const r in t)e[r]=(e[r]||0)-(t[r]||0)}function R(e){return typeof e=="boolean"||typeof e=="number"||e===null?!e:(0,s._)`!${q(e)}`}t.not=R;const M=z(t.operators.AND);function A(...e){return e.reduce(M)}t.and=A;const D=z(t.operators.OR);function V(...e){return e.reduce(D)}t.or=V;function z(e){return(t,r)=>t===s.nil?r:r===s.nil?t:(0,s._)`${q(t)} ${e} ${q(r)}`}function q(e){return e instanceof s.Name?e:(0,s._)`(${e})`}},57845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.ValueScope=t.ValueScopeName=t.Scope=t.varKinds=t.UsedValueState=void 0;const s=r(41520);class n extends Error{constructor(e){super(`CodeGen: "code" for ${e} not defined`);this.value=e.value}}var o;(function(e){e[e["Started"]=0]="Started";e[e["Completed"]=1]="Completed"})(o||(t.UsedValueState=o={}));t.varKinds={const:new s.Name("const"),let:new s.Name("let"),var:new s.Name("var")};class a{constructor({prefixes:e,parent:t}={}){this._names={};this._prefixes=e;this._parent=t}toName(e){return e instanceof s.Name?e:this.name(e)}name(e){return new s.Name(this._newName(e))}_newName(e){const t=this._names[e]||this._nameGroup(e);return`${e}${t.index++}`}_nameGroup(e){var t,r;if(((r=(t=this._parent)===null||t===void 0?void 0:t._prefixes)===null||r===void 0?void 0:r.has(e))||this._prefixes&&!this._prefixes.has(e)){throw new Error(`CodeGen: prefix "${e}" is not allowed in this scope`)}return this._names[e]={prefix:e,index:0}}}t.Scope=a;class i extends s.Name{constructor(e,t){super(t);this.prefix=e}setValue(e,{property:t,itemIndex:r}){this.value=e;this.scopePath=(0,s._)`.${new s.Name(t)}[${r}]`}}t.ValueScopeName=i;const c=(0,s._)`\n`;class u extends a{constructor(e){super(e);this._values={};this._scope=e.scope;this.opts={...e,_n:e.lines?c:s.nil}}get(){return this._scope}name(e){return new i(e,this._newName(e))}value(e,t){var r;if(t.ref===undefined)throw new Error("CodeGen: ref must be passed in value");const s=this.toName(e);const{prefix:n}=s;const o=(r=t.key)!==null&&r!==void 0?r:t.ref;let a=this._values[n];if(a){const e=a.get(o);if(e)return e}else{a=this._values[n]=new Map}a.set(o,s);const i=this._scope[n]||(this._scope[n]=[]);const c=i.length;i[c]=t.ref;s.setValue(t,{property:n,itemIndex:c});return s}getValue(e,t){const r=this._values[e];if(!r)return;return r.get(t)}scopeRefs(e,t=this._values){return this._reduceValues(t,(t=>{if(t.scopePath===undefined)throw new Error(`CodeGen: name "${t}" has no value`);return(0,s._)`${e}${t.scopePath}`}))}scopeCode(e=this._values,t,r){return this._reduceValues(e,(e=>{if(e.value===undefined)throw new Error(`CodeGen: name "${e}" has no value`);return e.value.code}),t,r)}_reduceValues(e,r,a={},i){let c=s.nil;for(const u in e){const d=e[u];if(!d)continue;const l=a[u]=a[u]||new Map;d.forEach((e=>{if(l.has(e))return;l.set(e,o.Started);let a=r(e);if(a){const r=this.opts.es5?t.varKinds.var:t.varKinds.const;c=(0,s._)`${c}${r} ${e} = ${a};${this.opts._n}`}else if(a=i===null||i===void 0?void 0:i(e)){c=(0,s._)`${c}${a}${this.opts._n}`}else{throw new n(e)}l.set(e,o.Completed)}))}return c}}t.ValueScope=u},48708:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.extendErrors=t.resetErrorsCount=t.reportExtraError=t.reportError=t.keyword$DataError=t.keywordError=void 0;const s=r(99029);const n=r(94227);const o=r(42023);t.keywordError={message:({keyword:e})=>(0,s.str)`must pass "${e}" keyword validation`};t.keyword$DataError={message:({keyword:e,schemaType:t})=>t?(0,s.str)`"${e}" keyword must be ${t} ($data)`:(0,s.str)`"${e}" keyword is invalid ($data)`};function a(e,r=t.keywordError,n,o){const{it:a}=e;const{gen:i,compositeRule:c,allErrors:u}=a;const f=h(e,r,n);if(o!==null&&o!==void 0?o:c||u){d(i,f)}else{l(a,(0,s._)`[${f}]`)}}t.reportError=a;function i(e,r=t.keywordError,s){const{it:n}=e;const{gen:a,compositeRule:i,allErrors:c}=n;const u=h(e,r,s);d(a,u);if(!(i||c)){l(n,o.default.vErrors)}}t.reportExtraError=i;function c(e,t){e.assign(o.default.errors,t);e.if((0,s._)`${o.default.vErrors} !== null`,(()=>e.if(t,(()=>e.assign((0,s._)`${o.default.vErrors}.length`,t)),(()=>e.assign(o.default.vErrors,null)))))}t.resetErrorsCount=c;function u({gen:e,keyword:t,schemaValue:r,data:n,errsCount:a,it:i}){if(a===undefined)throw new Error("ajv implementation error");const c=e.name("err");e.forRange("i",a,o.default.errors,(a=>{e.const(c,(0,s._)`${o.default.vErrors}[${a}]`);e.if((0,s._)`${c}.instancePath === undefined`,(()=>e.assign((0,s._)`${c}.instancePath`,(0,s.strConcat)(o.default.instancePath,i.errorPath))));e.assign((0,s._)`${c}.schemaPath`,(0,s.str)`${i.errSchemaPath}/${t}`);if(i.opts.verbose){e.assign((0,s._)`${c}.schema`,r);e.assign((0,s._)`${c}.data`,n)}}))}t.extendErrors=u;function d(e,t){const r=e.const("err",t);e.if((0,s._)`${o.default.vErrors} === null`,(()=>e.assign(o.default.vErrors,(0,s._)`[${r}]`)),(0,s._)`${o.default.vErrors}.push(${r})`);e.code((0,s._)`${o.default.errors}++`)}function l(e,t){const{gen:r,validateName:n,schemaEnv:o}=e;if(o.$async){r.throw((0,s._)`new ${e.ValidationError}(${t})`)}else{r.assign((0,s._)`${n}.errors`,t);r.return(false)}}const f={keyword:new s.Name("keyword"),schemaPath:new s.Name("schemaPath"),params:new s.Name("params"),propertyName:new s.Name("propertyName"),message:new s.Name("message"),schema:new s.Name("schema"),parentSchema:new s.Name("parentSchema")};function h(e,t,r){const{createErrors:n}=e.it;if(n===false)return(0,s._)`{}`;return p(e,t,r)}function p(e,t,r={}){const{gen:s,it:n}=e;const o=[m(n,r),y(e,r)];g(e,t,o);return s.object(...o)}function m({errorPath:e},{instancePath:t}){const r=t?(0,s.str)`${e}${(0,n.getErrorPath)(t,n.Type.Str)}`:e;return[o.default.instancePath,(0,s.strConcat)(o.default.instancePath,r)]}function y({keyword:e,it:{errSchemaPath:t}},{schemaPath:r,parentSchema:o}){let a=o?t:(0,s.str)`${t}/${e}`;if(r){a=(0,s.str)`${a}${(0,n.getErrorPath)(r,n.Type.Str)}`}return[f.schemaPath,a]}function g(e,{params:t,message:r},n){const{keyword:a,data:i,schemaValue:c,it:u}=e;const{opts:d,propertyName:l,topSchemaRef:h,schemaPath:p}=u;n.push([f.keyword,a],[f.params,typeof t=="function"?t(e):t||(0,s._)`{}`]);if(d.messages){n.push([f.message,typeof r=="function"?r(e):r])}if(d.verbose){n.push([f.schema,c],[f.parentSchema,(0,s._)`${h}${p}`],[o.default.data,i])}if(l)n.push([f.propertyName,l])}},73835:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.resolveSchema=t.getCompilingSchema=t.resolveRef=t.compileSchema=t.SchemaEnv=void 0;const s=r(99029);const n=r(13558);const o=r(42023);const a=r(66939);const i=r(94227);const c=r(62586);class u{constructor(e){var t;this.refs={};this.dynamicAnchors={};let r;if(typeof e.schema=="object")r=e.schema;this.schema=e.schema;this.schemaId=e.schemaId;this.root=e.root||this;this.baseId=(t=e.baseId)!==null&&t!==void 0?t:(0,a.normalizeId)(r===null||r===void 0?void 0:r[e.schemaId||"$id"]);this.schemaPath=e.schemaPath;this.localRefs=e.localRefs;this.meta=e.meta;this.$async=r===null||r===void 0?void 0:r.$async;this.refs={}}}t.SchemaEnv=u;function d(e){const t=h.call(this,e);if(t)return t;const r=(0,a.getFullPath)(this.opts.uriResolver,e.root.baseId);const{es5:i,lines:u}=this.opts.code;const{ownProperties:d}=this.opts;const l=new s.CodeGen(this.scope,{es5:i,lines:u,ownProperties:d});let f;if(e.$async){f=l.scopeValue("Error",{ref:n.default,code:(0,s._)`require("ajv/dist/runtime/validation_error").default`})}const p=l.scopeName("validate");e.validateName=p;const m={gen:l,allErrors:this.opts.allErrors,data:o.default.data,parentData:o.default.parentData,parentDataProperty:o.default.parentDataProperty,dataNames:[o.default.data],dataPathArr:[s.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:l.scopeValue("schema",this.opts.code.source===true?{ref:e.schema,code:(0,s.stringify)(e.schema)}:{ref:e.schema}),validateName:p,ValidationError:f,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:s.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:(0,s._)`""`,opts:this.opts,self:this};let y;try{this._compilations.add(e);(0,c.validateFunctionCode)(m);l.optimize(this.opts.code.optimize);const t=l.toString();y=`${l.scopeRefs(o.default.scope)}return ${t}`;if(this.opts.code.process)y=this.opts.code.process(y,e);const r=new Function(`${o.default.self}`,`${o.default.scope}`,y);const n=r(this,this.scope.get());this.scope.value(p,{ref:n});n.errors=null;n.schema=e.schema;n.schemaEnv=e;if(e.$async)n.$async=true;if(this.opts.code.source===true){n.source={validateName:p,validateCode:t,scopeValues:l._values}}if(this.opts.unevaluated){const{props:e,items:t}=m;n.evaluated={props:e instanceof s.Name?undefined:e,items:t instanceof s.Name?undefined:t,dynamicProps:e instanceof s.Name,dynamicItems:t instanceof s.Name};if(n.source)n.source.evaluated=(0,s.stringify)(n.evaluated)}e.validate=n;return e}catch(g){delete e.validate;delete e.validateName;if(y)this.logger.error("Error compiling schema, function code:",y);throw g}finally{this._compilations.delete(e)}}t.compileSchema=d;function l(e,t,r){var s;r=(0,a.resolveUrl)(this.opts.uriResolver,t,r);const n=e.refs[r];if(n)return n;let o=m.call(this,e,r);if(o===undefined){const n=(s=e.localRefs)===null||s===void 0?void 0:s[r];const{schemaId:a}=this.opts;if(n)o=new u({schema:n,schemaId:a,root:e,baseId:t})}if(o===undefined)return;return e.refs[r]=f.call(this,o)}t.resolveRef=l;function f(e){if((0,a.inlineRef)(e.schema,this.opts.inlineRefs))return e.schema;return e.validate?e:d.call(this,e)}function h(e){for(const t of this._compilations){if(p(t,e))return t}}t.getCompilingSchema=h;function p(e,t){return e.schema===t.schema&&e.root===t.root&&e.baseId===t.baseId}function m(e,t){let r;while(typeof(r=this.refs[t])=="string")t=r;return r||this.schemas[t]||y.call(this,e,t)}function y(e,t){const r=this.opts.uriResolver.parse(t);const s=(0,a._getFullPath)(this.opts.uriResolver,r);let n=(0,a.getFullPath)(this.opts.uriResolver,e.baseId,undefined);if(Object.keys(e.schema).length>0&&s===n){return $.call(this,r,e)}const o=(0,a.normalizeId)(s);const i=this.refs[o]||this.schemas[o];if(typeof i=="string"){const t=y.call(this,e,i);if(typeof(t===null||t===void 0?void 0:t.schema)!=="object")return;return $.call(this,r,t)}if(typeof(i===null||i===void 0?void 0:i.schema)!=="object")return;if(!i.validate)d.call(this,i);if(o===(0,a.normalizeId)(t)){const{schema:t}=i;const{schemaId:r}=this.opts;const s=t[r];if(s)n=(0,a.resolveUrl)(this.opts.uriResolver,n,s);return new u({schema:t,schemaId:r,root:e,baseId:n})}return $.call(this,r,i)}t.resolveSchema=y;const g=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function $(e,{baseId:t,schema:r,root:s}){var n;if(((n=e.fragment)===null||n===void 0?void 0:n[0])!=="/")return;for(const u of e.fragment.slice(1).split("/")){if(typeof r==="boolean")return;const e=r[(0,i.unescapeFragment)(u)];if(e===undefined)return;r=e;const s=typeof r==="object"&&r[this.opts.schemaId];if(!g.has(u)&&s){t=(0,a.resolveUrl)(this.opts.uriResolver,t,s)}}let o;if(typeof r!="boolean"&&r.$ref&&!(0,i.schemaHasRulesButRef)(r,this.RULES)){const e=(0,a.resolveUrl)(this.opts.uriResolver,t,r.$ref);o=y.call(this,s,e)}const{schemaId:c}=this.opts;o=o||new u({schema:r,schemaId:c,root:s,baseId:t});if(o.schema!==o.root.schema)return o;return undefined}},42023:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n={data:new s.Name("data"),valCxt:new s.Name("valCxt"),instancePath:new s.Name("instancePath"),parentData:new s.Name("parentData"),parentDataProperty:new s.Name("parentDataProperty"),rootData:new s.Name("rootData"),dynamicAnchors:new s.Name("dynamicAnchors"),vErrors:new s.Name("vErrors"),errors:new s.Name("errors"),this:new s.Name("this"),self:new s.Name("self"),scope:new s.Name("scope"),json:new s.Name("json"),jsonPos:new s.Name("jsonPos"),jsonLen:new s.Name("jsonLen"),jsonPart:new s.Name("jsonPart")};t["default"]=n},34551:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(66939);class n extends Error{constructor(e,t,r,n){super(n||`can't resolve reference ${r} from id ${t}`);this.missingRef=(0,s.resolveUrl)(e,t,r);this.missingSchema=(0,s.normalizeId)((0,s.getFullPath)(e,this.missingRef))}}t["default"]=n},66939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.getSchemaRefs=t.resolveUrl=t.normalizeId=t._getFullPath=t.getFullPath=t.inlineRef=void 0;const s=r(94227);const n=r(32017);const o=r(7106);const a=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);function i(e,t=true){if(typeof e=="boolean")return true;if(t===true)return!u(e);if(!t)return false;return d(e)<=t}t.inlineRef=i;const c=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function u(e){for(const t in e){if(c.has(t))return true;const r=e[t];if(Array.isArray(r)&&r.some(u))return true;if(typeof r=="object"&&u(r))return true}return false}function d(e){let t=0;for(const r in e){if(r==="$ref")return Infinity;t++;if(a.has(r))continue;if(typeof e[r]=="object"){(0,s.eachItem)(e[r],(e=>t+=d(e)))}if(t===Infinity)return Infinity}return t}function l(e,t="",r){if(r!==false)t=p(t);const s=e.parse(t);return f(e,s)}t.getFullPath=l;function f(e,t){const r=e.serialize(t);return r.split("#")[0]+"#"}t._getFullPath=f;const h=/#\/?$/;function p(e){return e?e.replace(h,""):""}t.normalizeId=p;function m(e,t,r){r=p(r);return e.resolve(t,r)}t.resolveUrl=m;const y=/^[a-z_][-a-z0-9._]*$/i;function g(e,t){if(typeof e=="boolean")return{};const{schemaId:r,uriResolver:s}=this.opts;const a=p(e[r]||t);const i={"":a};const c=l(s,a,false);const u={};const d=new Set;o(e,{allKeys:true},((e,t,s,n)=>{if(n===undefined)return;const o=c+t;let a=i[n];if(typeof e[r]=="string")a=l.call(this,e[r]);m.call(this,e.$anchor);m.call(this,e.$dynamicAnchor);i[t]=a;function l(t){const r=this.opts.uriResolver.resolve;t=p(a?r(a,t):t);if(d.has(t))throw h(t);d.add(t);let s=this.refs[t];if(typeof s=="string")s=this.refs[s];if(typeof s=="object"){f(e,s.schema,t)}else if(t!==p(o)){if(t[0]==="#"){f(e,u[t],t);u[t]=e}else{this.refs[t]=o}}return t}function m(e){if(typeof e=="string"){if(!y.test(e))throw new Error(`invalid anchor "${e}"`);l.call(this,`#${e}`)}}}));return u;function f(e,t,r){if(t!==undefined&&!n(e,t))throw h(r)}function h(e){return new Error(`reference "${e}" resolves to more than one schema`)}}t.getSchemaRefs=g},10396:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});t.getRules=t.isJSONType=void 0;const r=["string","number","integer","boolean","null","object","array"];const s=new Set(r);function n(e){return typeof e=="string"&&s.has(e)}t.isJSONType=n;function o(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:true,boolean:true,null:true},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}t.getRules=o},94227:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.checkStrictMode=t.getErrorPath=t.Type=t.useFunc=t.setEvaluated=t.evaluatedPropsToName=t.mergeEvaluated=t.eachItem=t.unescapeJsonPointer=t.escapeJsonPointer=t.escapeFragment=t.unescapeFragment=t.schemaRefOrVal=t.schemaHasRulesButRef=t.schemaHasRules=t.checkUnknownRules=t.alwaysValidSchema=t.toHash=void 0;const s=r(99029);const n=r(41520);function o(e){const t={};for(const r of e)t[r]=true;return t}t.toHash=o;function a(e,t){if(typeof t=="boolean")return t;if(Object.keys(t).length===0)return true;i(e,t);return!c(t,e.self.RULES.all)}t.alwaysValidSchema=a;function i(e,t=e.schema){const{opts:r,self:s}=e;if(!r.strictSchema)return;if(typeof t==="boolean")return;const n=s.RULES.keywords;for(const o in t){if(!n[o])P(e,`unknown keyword: "${o}"`)}}t.checkUnknownRules=i;function c(e,t){if(typeof e=="boolean")return!e;for(const r in e)if(t[r])return true;return false}t.schemaHasRules=c;function u(e,t){if(typeof e=="boolean")return!e;for(const r in e)if(r!=="$ref"&&t.all[r])return true;return false}t.schemaHasRulesButRef=u;function d({topSchemaRef:e,schemaPath:t},r,n,o){if(!o){if(typeof r=="number"||typeof r=="boolean")return r;if(typeof r=="string")return(0,s._)`${r}`}return(0,s._)`${e}${t}${(0,s.getProperty)(n)}`}t.schemaRefOrVal=d;function l(e){return p(decodeURIComponent(e))}t.unescapeFragment=l;function f(e){return encodeURIComponent(h(e))}t.escapeFragment=f;function h(e){if(typeof e=="number")return`${e}`;return e.replace(/~/g,"~0").replace(/\//g,"~1")}t.escapeJsonPointer=h;function p(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}t.unescapeJsonPointer=p;function m(e,t){if(Array.isArray(e)){for(const r of e)t(r)}else{t(e)}}t.eachItem=m;function y({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:n}){return(o,a,i,c)=>{const u=i===undefined?a:i instanceof s.Name?(a instanceof s.Name?e(o,a,i):t(o,a,i),i):a instanceof s.Name?(t(o,i,a),a):r(a,i);return c===s.Name&&!(u instanceof s.Name)?n(o,u):u}}t.mergeEvaluated={props:y({mergeNames:(e,t,r)=>e.if((0,s._)`${r} !== true && ${t} !== undefined`,(()=>{e.if((0,s._)`${t} === true`,(()=>e.assign(r,true)),(()=>e.assign(r,(0,s._)`${r} || {}`).code((0,s._)`Object.assign(${r}, ${t})`)))})),mergeToName:(e,t,r)=>e.if((0,s._)`${r} !== true`,(()=>{if(t===true){e.assign(r,true)}else{e.assign(r,(0,s._)`${r} || {}`);$(e,r,t)}})),mergeValues:(e,t)=>e===true?true:{...e,...t},resultToName:g}),items:y({mergeNames:(e,t,r)=>e.if((0,s._)`${r} !== true && ${t} !== undefined`,(()=>e.assign(r,(0,s._)`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`))),mergeToName:(e,t,r)=>e.if((0,s._)`${r} !== true`,(()=>e.assign(r,t===true?true:(0,s._)`${r} > ${t} ? ${r} : ${t}`))),mergeValues:(e,t)=>e===true?true:Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})};function g(e,t){if(t===true)return e.var("props",true);const r=e.var("props",(0,s._)`{}`);if(t!==undefined)$(e,r,t);return r}t.evaluatedPropsToName=g;function $(e,t,r){Object.keys(r).forEach((r=>e.assign((0,s._)`${t}${(0,s.getProperty)(r)}`,true)))}t.setEvaluated=$;const v={};function _(e,t){return e.scopeValue("func",{ref:t,code:v[t.code]||(v[t.code]=new n._Code(t.code))})}t.useFunc=_;var w;(function(e){e[e["Num"]=0]="Num";e[e["Str"]=1]="Str"})(w||(t.Type=w={}));function b(e,t,r){if(e instanceof s.Name){const n=t===w.Num;return r?n?(0,s._)`"[" + ${e} + "]"`:(0,s._)`"['" + ${e} + "']"`:n?(0,s._)`"/" + ${e}`:(0,s._)`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,s.getProperty)(e).toString():"/"+h(e)}t.getErrorPath=b;function P(e,t,r=e.opts.strictSchema){if(!r)return;t=`strict mode: ${t}`;if(r===true)throw new Error(t);e.self.logger.warn(t)}t.checkStrictMode=P},7887:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});t.shouldUseRule=t.shouldUseGroup=t.schemaHasRulesForType=void 0;function r({schema:e,self:t},r){const n=t.RULES.types[r];return n&&n!==true&&s(e,n)}t.schemaHasRulesForType=r;function s(e,t){return t.rules.some((t=>n(e,t)))}t.shouldUseGroup=s;function n(e,t){var r;return e[t.keyword]!==undefined||((r=t.definition.implements)===null||r===void 0?void 0:r.some((t=>e[t]!==undefined)))}t.shouldUseRule=n},28727:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.boolOrEmptySchema=t.topBoolOrEmptySchema=void 0;const s=r(48708);const n=r(99029);const o=r(42023);const a={message:"boolean schema is false"};function i(e){const{gen:t,schema:r,validateName:s}=e;if(r===false){u(e,false)}else if(typeof r=="object"&&r.$async===true){t.return(o.default.data)}else{t.assign((0,n._)`${s}.errors`,null);t.return(true)}}t.topBoolOrEmptySchema=i;function c(e,t){const{gen:r,schema:s}=e;if(s===false){r.var(t,false);u(e)}else{r.var(t,true)}}t.boolOrEmptySchema=c;function u(e,t){const{gen:r,data:n}=e;const o={gen:r,keyword:"false schema",data:n,schema:false,schemaCode:false,schemaValue:false,params:{},it:e};(0,s.reportError)(o,a,undefined,t)}},10208:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.reportTypeError=t.checkDataTypes=t.checkDataType=t.coerceAndCheckDataType=t.getJSONTypes=t.getSchemaTypes=t.DataType=void 0;const s=r(10396);const n=r(7887);const o=r(48708);const a=r(99029);const i=r(94227);var c;(function(e){e[e["Correct"]=0]="Correct";e[e["Wrong"]=1]="Wrong"})(c||(t.DataType=c={}));function u(e){const t=d(e.type);const r=t.includes("null");if(r){if(e.nullable===false)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&e.nullable!==undefined){throw new Error('"nullable" cannot be used without "type"')}if(e.nullable===true)t.push("null")}return t}t.getSchemaTypes=u;function d(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(s.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}t.getJSONTypes=d;function l(e,t){const{gen:r,data:s,opts:o}=e;const a=h(t,o.coerceTypes);const i=t.length>0&&!(a.length===0&&t.length===1&&(0,n.schemaHasRulesForType)(e,t[0]));if(i){const n=g(t,s,o.strictNumbers,c.Wrong);r.if(n,(()=>{if(a.length)p(e,t,a);else v(e)}))}return i}t.coerceAndCheckDataType=l;const f=new Set(["string","number","integer","boolean","null"]);function h(e,t){return t?e.filter((e=>f.has(e)||t==="array"&&e==="array")):[]}function p(e,t,r){const{gen:s,data:n,opts:o}=e;const i=s.let("dataType",(0,a._)`typeof ${n}`);const c=s.let("coerced",(0,a._)`undefined`);if(o.coerceTypes==="array"){s.if((0,a._)`${i} == 'object' && Array.isArray(${n}) && ${n}.length == 1`,(()=>s.assign(n,(0,a._)`${n}[0]`).assign(i,(0,a._)`typeof ${n}`).if(g(t,n,o.strictNumbers),(()=>s.assign(c,n)))))}s.if((0,a._)`${c} !== undefined`);for(const a of r){if(f.has(a)||a==="array"&&o.coerceTypes==="array"){u(a)}}s.else();v(e);s.endIf();s.if((0,a._)`${c} !== undefined`,(()=>{s.assign(n,c);m(e,c)}));function u(e){switch(e){case"string":s.elseIf((0,a._)`${i} == "number" || ${i} == "boolean"`).assign(c,(0,a._)`"" + ${n}`).elseIf((0,a._)`${n} === null`).assign(c,(0,a._)`""`);return;case"number":s.elseIf((0,a._)`${i} == "boolean" || ${n} === null
              || (${i} == "string" && ${n} && ${n} == +${n})`).assign(c,(0,a._)`+${n}`);return;case"integer":s.elseIf((0,a._)`${i} === "boolean" || ${n} === null
              || (${i} === "string" && ${n} && ${n} == +${n} && !(${n} % 1))`).assign(c,(0,a._)`+${n}`);return;case"boolean":s.elseIf((0,a._)`${n} === "false" || ${n} === 0 || ${n} === null`).assign(c,false).elseIf((0,a._)`${n} === "true" || ${n} === 1`).assign(c,true);return;case"null":s.elseIf((0,a._)`${n} === "" || ${n} === 0 || ${n} === false`);s.assign(c,null);return;case"array":s.elseIf((0,a._)`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${n} === null`).assign(c,(0,a._)`[${n}]`)}}}function m({gen:e,parentData:t,parentDataProperty:r},s){e.if((0,a._)`${t} !== undefined`,(()=>e.assign((0,a._)`${t}[${r}]`,s)))}function y(e,t,r,s=c.Correct){const n=s===c.Correct?a.operators.EQ:a.operators.NEQ;let o;switch(e){case"null":return(0,a._)`${t} ${n} null`;case"array":o=(0,a._)`Array.isArray(${t})`;break;case"object":o=(0,a._)`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":o=i((0,a._)`!(${t} % 1) && !isNaN(${t})`);break;case"number":o=i();break;default:return(0,a._)`typeof ${t} ${n} ${e}`}return s===c.Correct?o:(0,a.not)(o);function i(e=a.nil){return(0,a.and)((0,a._)`typeof ${t} == "number"`,e,r?(0,a._)`isFinite(${t})`:a.nil)}}t.checkDataType=y;function g(e,t,r,s){if(e.length===1){return y(e[0],t,r,s)}let n;const o=(0,i.toHash)(e);if(o.array&&o.object){const e=(0,a._)`typeof ${t} != "object"`;n=o.null?e:(0,a._)`!${t} || ${e}`;delete o.null;delete o.array;delete o.object}else{n=a.nil}if(o.number)delete o.integer;for(const i in o)n=(0,a.and)(n,y(i,t,r,s));return n}t.checkDataTypes=g;const $={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>typeof e=="string"?(0,a._)`{type: ${e}}`:(0,a._)`{type: ${t}}`};function v(e){const t=_(e);(0,o.reportError)(t,$)}t.reportTypeError=v;function _(e){const{gen:t,data:r,schema:s}=e;const n=(0,i.schemaRefOrVal)(e,s,"type");return{gen:t,keyword:"type",data:r,schema:s.type,schemaCode:n,schemaValue:n,parentSchema:s,params:{},it:e}}},7870:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.assignDefaults=void 0;const s=r(99029);const n=r(94227);function o(e,t){const{properties:r,items:s}=e.schema;if(t==="object"&&r){for(const t in r){a(e,t,r[t].default)}}else if(t==="array"&&Array.isArray(s)){s.forEach(((t,r)=>a(e,r,t.default)))}}t.assignDefaults=o;function a(e,t,r){const{gen:o,compositeRule:a,data:i,opts:c}=e;if(r===undefined)return;const u=(0,s._)`${i}${(0,s.getProperty)(t)}`;if(a){(0,n.checkStrictMode)(e,`default is ignored for: ${u}`);return}let d=(0,s._)`${u} === undefined`;if(c.useDefaults==="empty"){d=(0,s._)`${d} || ${u} === null || ${u} === ""`}o.if(d,(0,s._)`${u} = ${(0,s.stringify)(r)}`)}},62586:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.getData=t.KeywordCxt=t.validateFunctionCode=void 0;const s=r(28727);const n=r(10208);const o=r(7887);const a=r(10208);const i=r(7870);const c=r(33673);const u=r(24495);const d=r(99029);const l=r(42023);const f=r(66939);const h=r(94227);const p=r(48708);function m(e){if(E(e)){k(e);if(P(e)){v(e);return}}y(e,(()=>(0,s.topBoolOrEmptySchema)(e)))}t.validateFunctionCode=m;function y({gen:e,validateName:t,schema:r,schemaEnv:s,opts:n},o){if(n.code.es5){e.func(t,(0,d._)`${l.default.data}, ${l.default.valCxt}`,s.$async,(()=>{e.code((0,d._)`"use strict"; ${w(r,n)}`);$(e,n);e.code(o)}))}else{e.func(t,(0,d._)`${l.default.data}, ${g(n)}`,s.$async,(()=>e.code(w(r,n)).code(o)))}}function g(e){return(0,d._)`{${l.default.instancePath}="", ${l.default.parentData}, ${l.default.parentDataProperty}, ${l.default.rootData}=${l.default.data}${e.dynamicRef?(0,d._)`, ${l.default.dynamicAnchors}={}`:d.nil}}={}`}function $(e,t){e.if(l.default.valCxt,(()=>{e.var(l.default.instancePath,(0,d._)`${l.default.valCxt}.${l.default.instancePath}`);e.var(l.default.parentData,(0,d._)`${l.default.valCxt}.${l.default.parentData}`);e.var(l.default.parentDataProperty,(0,d._)`${l.default.valCxt}.${l.default.parentDataProperty}`);e.var(l.default.rootData,(0,d._)`${l.default.valCxt}.${l.default.rootData}`);if(t.dynamicRef)e.var(l.default.dynamicAnchors,(0,d._)`${l.default.valCxt}.${l.default.dynamicAnchors}`)}),(()=>{e.var(l.default.instancePath,(0,d._)`""`);e.var(l.default.parentData,(0,d._)`undefined`);e.var(l.default.parentDataProperty,(0,d._)`undefined`);e.var(l.default.rootData,l.default.data);if(t.dynamicRef)e.var(l.default.dynamicAnchors,(0,d._)`{}`)}))}function v(e){const{schema:t,opts:r,gen:s}=e;y(e,(()=>{if(r.$comment&&t.$comment)x(e);O(e);s.let(l.default.vErrors,null);s.let(l.default.errors,0);if(r.unevaluated)_(e);N(e);T(e)}));return}function _(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",(0,d._)`${r}.evaluated`);t.if((0,d._)`${e.evaluated}.dynamicProps`,(()=>t.assign((0,d._)`${e.evaluated}.props`,(0,d._)`undefined`)));t.if((0,d._)`${e.evaluated}.dynamicItems`,(()=>t.assign((0,d._)`${e.evaluated}.items`,(0,d._)`undefined`)))}function w(e,t){const r=typeof e=="object"&&e[t.schemaId];return r&&(t.code.source||t.code.process)?(0,d._)`/*# sourceURL=${r} */`:d.nil}function b(e,t){if(E(e)){k(e);if(P(e)){S(e,t);return}}(0,s.boolOrEmptySchema)(e,t)}function P({schema:e,self:t}){if(typeof e=="boolean")return!e;for(const r in e)if(t.RULES.all[r])return true;return false}function E(e){return typeof e.schema!="boolean"}function S(e,t){const{schema:r,gen:s,opts:n}=e;if(n.$comment&&r.$comment)x(e);C(e);I(e);const o=s.const("_errs",l.default.errors);N(e,o);s.var(t,(0,d._)`${o} === ${l.default.errors}`)}function k(e){(0,h.checkUnknownRules)(e);j(e)}function N(e,t){if(e.opts.jtd)return M(e,[],false,t);const r=(0,n.getSchemaTypes)(e.schema);const s=(0,n.coerceAndCheckDataType)(e,r);M(e,r,!s,t)}function j(e){const{schema:t,errSchemaPath:r,opts:s,self:n}=e;if(t.$ref&&s.ignoreKeywordsWithRef&&(0,h.schemaHasRulesButRef)(t,n.RULES)){n.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}}function O(e){const{schema:t,opts:r}=e;if(t.default!==undefined&&r.useDefaults&&r.strictSchema){(0,h.checkStrictMode)(e,"default is ignored in the schema root")}}function C(e){const t=e.schema[e.opts.schemaId];if(t)e.baseId=(0,f.resolveUrl)(e.opts.uriResolver,e.baseId,t)}function I(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}function x({gen:e,schemaEnv:t,schema:r,errSchemaPath:s,opts:n}){const o=r.$comment;if(n.$comment===true){e.code((0,d._)`${l.default.self}.logger.log(${o})`)}else if(typeof n.$comment=="function"){const r=(0,d.str)`${s}/$comment`;const n=e.scopeValue("root",{ref:t.root});e.code((0,d._)`${l.default.self}.opts.$comment(${o}, ${r}, ${n}.schema)`)}}function T(e){const{gen:t,schemaEnv:r,validateName:s,ValidationError:n,opts:o}=e;if(r.$async){t.if((0,d._)`${l.default.errors} === 0`,(()=>t.return(l.default.data)),(()=>t.throw((0,d._)`new ${n}(${l.default.vErrors})`)))}else{t.assign((0,d._)`${s}.errors`,l.default.vErrors);if(o.unevaluated)R(e);t.return((0,d._)`${l.default.errors} === 0`)}}function R({gen:e,evaluated:t,props:r,items:s}){if(r instanceof d.Name)e.assign((0,d._)`${t}.props`,r);if(s instanceof d.Name)e.assign((0,d._)`${t}.items`,s)}function M(e,t,r,s){const{gen:n,schema:i,data:c,allErrors:u,opts:f,self:p}=e;const{RULES:m}=p;if(i.$ref&&(f.ignoreKeywordsWithRef||!(0,h.schemaHasRulesButRef)(i,m))){n.block((()=>H(e,"$ref",m.all.$ref.definition)));return}if(!f.jtd)D(e,t);n.block((()=>{for(const e of m.rules)y(e);y(m.post)}));function y(h){if(!(0,o.shouldUseGroup)(i,h))return;if(h.type){n.if((0,a.checkDataType)(h.type,c,f.strictNumbers));A(e,h);if(t.length===1&&t[0]===h.type&&r){n.else();(0,a.reportTypeError)(e)}n.endIf()}else{A(e,h)}if(!u)n.if((0,d._)`${l.default.errors} === ${s||0}`)}}function A(e,t){const{gen:r,schema:s,opts:{useDefaults:n}}=e;if(n)(0,i.assignDefaults)(e,t.type);r.block((()=>{for(const r of t.rules){if((0,o.shouldUseRule)(s,r)){H(e,r.keyword,r.definition,t.type)}}}))}function D(e,t){if(e.schemaEnv.meta||!e.opts.strictTypes)return;V(e,t);if(!e.opts.allowUnionTypes)z(e,t);q(e,e.dataTypes)}function V(e,t){if(!t.length)return;if(!e.dataTypes.length){e.dataTypes=t;return}t.forEach((t=>{if(!K(e.dataTypes,t)){F(e,`type "${t}" not allowed by context "${e.dataTypes.join(",")}"`)}}));L(e,t)}function z(e,t){if(t.length>1&&!(t.length===2&&t.includes("null"))){F(e,"use allowUnionTypes to allow union type keyword")}}function q(e,t){const r=e.self.RULES.all;for(const s in r){const n=r[s];if(typeof n=="object"&&(0,o.shouldUseRule)(e.schema,n)){const{type:r}=n.definition;if(r.length&&!r.some((e=>U(t,e)))){F(e,`missing type "${r.join(",")}" for keyword "${s}"`)}}}}function U(e,t){return e.includes(t)||t==="number"&&e.includes("integer")}function K(e,t){return e.includes(t)||t==="integer"&&e.includes("number")}function L(e,t){const r=[];for(const s of e.dataTypes){if(K(t,s))r.push(s);else if(t.includes("integer")&&s==="number")r.push("integer")}e.dataTypes=r}function F(e,t){const r=e.schemaEnv.baseId+e.errSchemaPath;t+=` at "${r}" (strictTypes)`;(0,h.checkStrictMode)(e,t,e.opts.strictTypes)}class G{constructor(e,t,r){(0,c.validateKeywordUsage)(e,t,r);this.gen=e.gen;this.allErrors=e.allErrors;this.keyword=r;this.data=e.data;this.schema=e.schema[r];this.$data=t.$data&&e.opts.$data&&this.schema&&this.schema.$data;this.schemaValue=(0,h.schemaRefOrVal)(e,this.schema,r,this.$data);this.schemaType=t.schemaType;this.parentSchema=e.schema;this.params={};this.it=e;this.def=t;if(this.$data){this.schemaCode=e.gen.const("vSchema",W(this.$data,e))}else{this.schemaCode=this.schemaValue;if(!(0,c.validSchemaType)(this.schema,t.schemaType,t.allowUndefined)){throw new Error(`${r} value must be ${JSON.stringify(t.schemaType)}`)}}if("code"in t?t.trackErrors:t.errors!==false){this.errsCount=e.gen.const("_errs",l.default.errors)}}result(e,t,r){this.failResult((0,d.not)(e),t,r)}failResult(e,t,r){this.gen.if(e);if(r)r();else this.error();if(t){this.gen.else();t();if(this.allErrors)this.gen.endIf()}else{if(this.allErrors)this.gen.endIf();else this.gen.else()}}pass(e,t){this.failResult((0,d.not)(e),undefined,t)}fail(e){if(e===undefined){this.error();if(!this.allErrors)this.gen.if(false);return}this.gen.if(e);this.error();if(this.allErrors)this.gen.endIf();else this.gen.else()}fail$data(e){if(!this.$data)return this.fail(e);const{schemaCode:t}=this;this.fail((0,d._)`${t} !== undefined && (${(0,d.or)(this.invalid$data(),e)})`)}error(e,t,r){if(t){this.setParams(t);this._error(e,r);this.setParams({});return}this._error(e,r)}_error(e,t){(e?p.reportExtraError:p.reportError)(this,this.def.error,t)}$dataError(){(0,p.reportError)(this,this.def.$dataError||p.keyword$DataError)}reset(){if(this.errsCount===undefined)throw new Error('add "trackErrors" to keyword definition');(0,p.resetErrorsCount)(this.gen,this.errsCount)}ok(e){if(!this.allErrors)this.gen.if(e)}setParams(e,t){if(t)Object.assign(this.params,e);else this.params=e}block$data(e,t,r=d.nil){this.gen.block((()=>{this.check$data(e,r);t()}))}check$data(e=d.nil,t=d.nil){if(!this.$data)return;const{gen:r,schemaCode:s,schemaType:n,def:o}=this;r.if((0,d.or)((0,d._)`${s} === undefined`,t));if(e!==d.nil)r.assign(e,true);if(n.length||o.validateSchema){r.elseIf(this.invalid$data());this.$dataError();if(e!==d.nil)r.assign(e,false)}r.else()}invalid$data(){const{gen:e,schemaCode:t,schemaType:r,def:s,it:n}=this;return(0,d.or)(o(),i());function o(){if(r.length){if(!(t instanceof d.Name))throw new Error("ajv implementation error");const e=Array.isArray(r)?r:[r];return(0,d._)`${(0,a.checkDataTypes)(e,t,n.opts.strictNumbers,a.DataType.Wrong)}`}return d.nil}function i(){if(s.validateSchema){const r=e.scopeValue("validate$data",{ref:s.validateSchema});return(0,d._)`!${r}(${t})`}return d.nil}}subschema(e,t){const r=(0,u.getSubschema)(this.it,e);(0,u.extendSubschemaData)(r,this.it,e);(0,u.extendSubschemaMode)(r,e);const s={...this.it,...r,items:undefined,props:undefined};b(s,t);return s}mergeEvaluated(e,t){const{it:r,gen:s}=this;if(!r.opts.unevaluated)return;if(r.props!==true&&e.props!==undefined){r.props=h.mergeEvaluated.props(s,e.props,r.props,t)}if(r.items!==true&&e.items!==undefined){r.items=h.mergeEvaluated.items(s,e.items,r.items,t)}}mergeValidEvaluated(e,t){const{it:r,gen:s}=this;if(r.opts.unevaluated&&(r.props!==true||r.items!==true)){s.if(t,(()=>this.mergeEvaluated(e,d.Name)));return true}}}t.KeywordCxt=G;function H(e,t,r,s){const n=new G(e,r,t);if("code"in r){r.code(n,s)}else if(n.$data&&r.validate){(0,c.funcKeywordCode)(n,r)}else if("macro"in r){(0,c.macroKeywordCode)(n,r)}else if(r.compile||r.validate){(0,c.funcKeywordCode)(n,r)}}const J=/^\/(?:[^~]|~0|~1)*$/;const B=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function W(e,{dataLevel:t,dataNames:r,dataPathArr:s}){let n;let o;if(e==="")return l.default.rootData;if(e[0]==="/"){if(!J.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);n=e;o=l.default.rootData}else{const a=B.exec(e);if(!a)throw new Error(`Invalid JSON-pointer: ${e}`);const i=+a[1];n=a[2];if(n==="#"){if(i>=t)throw new Error(c("property/index",i));return s[t-i]}if(i>t)throw new Error(c("data",i));o=r[t-i];if(!n)return o}let a=o;const i=n.split("/");for(const u of i){if(u){o=(0,d._)`${o}${(0,d.getProperty)((0,h.unescapeJsonPointer)(u))}`;a=(0,d._)`${a} && ${o}`}}return a;function c(e,r){return`Cannot access ${e} ${r} levels up, current level is ${t}`}}t.getData=W},33673:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.validateKeywordUsage=t.validSchemaType=t.funcKeywordCode=t.macroKeywordCode=void 0;const s=r(99029);const n=r(42023);const o=r(15765);const a=r(48708);function i(e,t){const{gen:r,keyword:n,schema:o,parentSchema:a,it:i}=e;const c=t.macro.call(i.self,o,a,i);const u=f(r,n,c);if(i.opts.validateSchema!==false)i.self.validateSchema(c,true);const d=r.name("valid");e.subschema({schema:c,schemaPath:s.nil,errSchemaPath:`${i.errSchemaPath}/${n}`,topSchemaRef:u,compositeRule:true},d);e.pass(d,(()=>e.error(true)))}t.macroKeywordCode=i;function c(e,t){var r;const{gen:a,keyword:i,schema:c,parentSchema:h,$data:p,it:m}=e;l(m,t);const y=!p&&t.compile?t.compile.call(m.self,c,h,m):t.validate;const g=f(a,i,y);const $=a.let("valid");e.block$data($,v);e.ok((r=t.valid)!==null&&r!==void 0?r:$);function v(){if(t.errors===false){b();if(t.modifying)u(e);P((()=>e.error()))}else{const r=t.async?_():w();if(t.modifying)u(e);P((()=>d(e,r)))}}function _(){const e=a.let("ruleErrs",null);a.try((()=>b((0,s._)`await `)),(t=>a.assign($,false).if((0,s._)`${t} instanceof ${m.ValidationError}`,(()=>a.assign(e,(0,s._)`${t}.errors`)),(()=>a.throw(t)))));return e}function w(){const e=(0,s._)`${g}.errors`;a.assign(e,null);b(s.nil);return e}function b(r=(t.async?(0,s._)`await `:s.nil)){const i=m.opts.passContext?n.default.this:n.default.self;const c=!("compile"in t&&!p||t.schema===false);a.assign($,(0,s._)`${r}${(0,o.callValidateCode)(e,g,i,c)}`,t.modifying)}function P(e){var r;a.if((0,s.not)((r=t.valid)!==null&&r!==void 0?r:$),e)}}t.funcKeywordCode=c;function u(e){const{gen:t,data:r,it:n}=e;t.if(n.parentData,(()=>t.assign(r,(0,s._)`${n.parentData}[${n.parentDataProperty}]`)))}function d(e,t){const{gen:r}=e;r.if((0,s._)`Array.isArray(${t})`,(()=>{r.assign(n.default.vErrors,(0,s._)`${n.default.vErrors} === null ? ${t} : ${n.default.vErrors}.concat(${t})`).assign(n.default.errors,(0,s._)`${n.default.vErrors}.length`);(0,a.extendErrors)(e)}),(()=>e.error()))}function l({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}function f(e,t,r){if(r===undefined)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword",typeof r=="function"?{ref:r}:{ref:r,code:(0,s.stringify)(r)})}function h(e,t,r=false){return!t.length||t.some((t=>t==="array"?Array.isArray(e):t==="object"?e&&typeof e=="object"&&!Array.isArray(e):typeof e==t||r&&typeof e=="undefined"))}t.validSchemaType=h;function p({schema:e,opts:t,self:r,errSchemaPath:s},n,o){if(Array.isArray(n.keyword)?!n.keyword.includes(o):n.keyword!==o){throw new Error("ajv implementation error")}const a=n.dependencies;if(a===null||a===void 0?void 0:a.some((t=>!Object.prototype.hasOwnProperty.call(e,t)))){throw new Error(`parent schema must have dependencies of ${o}: ${a.join(",")}`)}if(n.validateSchema){const a=n.validateSchema(e[o]);if(!a){const e=`keyword "${o}" value is invalid at path "${s}": `+r.errorsText(n.validateSchema.errors);if(t.validateSchema==="log")r.logger.error(e);else throw new Error(e)}}}t.validateKeywordUsage=p},24495:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.extendSubschemaMode=t.extendSubschemaData=t.getSubschema=void 0;const s=r(99029);const n=r(94227);function o(e,{keyword:t,schemaProp:r,schema:o,schemaPath:a,errSchemaPath:i,topSchemaRef:c}){if(t!==undefined&&o!==undefined){throw new Error('both "keyword" and "schema" passed, only one allowed')}if(t!==undefined){const o=e.schema[t];return r===undefined?{schema:o,schemaPath:(0,s._)`${e.schemaPath}${(0,s.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:o[r],schemaPath:(0,s._)`${e.schemaPath}${(0,s.getProperty)(t)}${(0,s.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,n.escapeFragment)(r)}`}}if(o!==undefined){if(a===undefined||i===undefined||c===undefined){throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"')}return{schema:o,schemaPath:a,topSchemaRef:c,errSchemaPath:i}}throw new Error('either "keyword" or "schema" must be passed')}t.getSubschema=o;function a(e,t,{dataProp:r,dataPropType:o,data:a,dataTypes:i,propertyName:c}){if(a!==undefined&&r!==undefined){throw new Error('both "data" and "dataProp" passed, only one allowed')}const{gen:u}=t;if(r!==undefined){const{errorPath:a,dataPathArr:i,opts:c}=t;const l=u.let("data",(0,s._)`${t.data}${(0,s.getProperty)(r)}`,true);d(l);e.errorPath=(0,s.str)`${a}${(0,n.getErrorPath)(r,o,c.jsPropertySyntax)}`;e.parentDataProperty=(0,s._)`${r}`;e.dataPathArr=[...i,e.parentDataProperty]}if(a!==undefined){const t=a instanceof s.Name?a:u.let("data",a,true);d(t);if(c!==undefined)e.propertyName=c}if(i)e.dataTypes=i;function d(r){e.data=r;e.dataLevel=t.dataLevel+1;e.dataTypes=[];t.definedProperties=new Set;e.parentData=t.data;e.dataNames=[...t.dataNames,r]}}t.extendSubschemaData=a;function i(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:s,createErrors:n,allErrors:o}){if(s!==undefined)e.compositeRule=s;if(n!==undefined)e.createErrors=n;if(o!==undefined)e.allErrors=o;e.jtdDiscriminator=t;e.jtdMetadata=r}t.extendSubschemaMode=i},4042:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=void 0;var s=r(62586);Object.defineProperty(t,"KeywordCxt",{enumerable:true,get:function(){return s.KeywordCxt}});var n=r(99029);Object.defineProperty(t,"_",{enumerable:true,get:function(){return n._}});Object.defineProperty(t,"str",{enumerable:true,get:function(){return n.str}});Object.defineProperty(t,"stringify",{enumerable:true,get:function(){return n.stringify}});Object.defineProperty(t,"nil",{enumerable:true,get:function(){return n.nil}});Object.defineProperty(t,"Name",{enumerable:true,get:function(){return n.Name}});Object.defineProperty(t,"CodeGen",{enumerable:true,get:function(){return n.CodeGen}});const o=r(13558);const a=r(34551);const i=r(10396);const c=r(73835);const u=r(99029);const d=r(66939);const l=r(10208);const f=r(94227);const h=r(63837);const p=r(55944);const m=(e,t)=>new RegExp(e,t);m.code="new RegExp";const y=["removeAdditional","useDefaults","coerceTypes"];const g=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]);const $={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."};const v={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'};const _=200;function w(e){var t,r,s,n,o,a,i,c,u,d,l,f,h,y,g,$,v,w,b,P,E,S,k,N,j;const O=e.strict;const C=(t=e.code)===null||t===void 0?void 0:t.optimize;const I=C===true||C===undefined?1:C||0;const x=(s=(r=e.code)===null||r===void 0?void 0:r.regExp)!==null&&s!==void 0?s:m;const T=(n=e.uriResolver)!==null&&n!==void 0?n:p.default;return{strictSchema:(a=(o=e.strictSchema)!==null&&o!==void 0?o:O)!==null&&a!==void 0?a:true,strictNumbers:(c=(i=e.strictNumbers)!==null&&i!==void 0?i:O)!==null&&c!==void 0?c:true,strictTypes:(d=(u=e.strictTypes)!==null&&u!==void 0?u:O)!==null&&d!==void 0?d:"log",strictTuples:(f=(l=e.strictTuples)!==null&&l!==void 0?l:O)!==null&&f!==void 0?f:"log",strictRequired:(y=(h=e.strictRequired)!==null&&h!==void 0?h:O)!==null&&y!==void 0?y:false,code:e.code?{...e.code,optimize:I,regExp:x}:{optimize:I,regExp:x},loopRequired:(g=e.loopRequired)!==null&&g!==void 0?g:_,loopEnum:($=e.loopEnum)!==null&&$!==void 0?$:_,meta:(v=e.meta)!==null&&v!==void 0?v:true,messages:(w=e.messages)!==null&&w!==void 0?w:true,inlineRefs:(b=e.inlineRefs)!==null&&b!==void 0?b:true,schemaId:(P=e.schemaId)!==null&&P!==void 0?P:"$id",addUsedSchema:(E=e.addUsedSchema)!==null&&E!==void 0?E:true,validateSchema:(S=e.validateSchema)!==null&&S!==void 0?S:true,validateFormats:(k=e.validateFormats)!==null&&k!==void 0?k:true,unicodeRegExp:(N=e.unicodeRegExp)!==null&&N!==void 0?N:true,int32range:(j=e.int32range)!==null&&j!==void 0?j:true,uriResolver:T}}class b{constructor(e={}){this.schemas={};this.refs={};this.formats={};this._compilations=new Set;this._loading={};this._cache=new Map;e=this.opts={...e,...w(e)};const{es5:t,lines:r}=this.opts.code;this.scope=new u.ValueScope({scope:{},prefixes:g,es5:t,lines:r});this.logger=C(e.logger);const s=e.validateFormats;e.validateFormats=false;this.RULES=(0,i.getRules)();P.call(this,$,e,"NOT SUPPORTED");P.call(this,v,e,"DEPRECATED","warn");this._metaOpts=j.call(this);if(e.formats)k.call(this);this._addVocabularies();this._addDefaultMetaSchema();if(e.keywords)N.call(this,e.keywords);if(typeof e.meta=="object")this.addMetaSchema(e.meta);S.call(this);e.validateFormats=s}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:e,meta:t,schemaId:r}=this.opts;let s=h;if(r==="id"){s={...h};s.id=s.$id;delete s.$id}if(t&&e)this.addMetaSchema(s,s[r],false)}defaultMeta(){const{meta:e,schemaId:t}=this.opts;return this.opts.defaultMeta=typeof e=="object"?e[t]||e:undefined}validate(e,t){let r;if(typeof e=="string"){r=this.getSchema(e);if(!r)throw new Error(`no schema with key or ref "${e}"`)}else{r=this.compile(e)}const s=r(t);if(!("$async"in r))this.errors=r.errors;return s}compile(e,t){const r=this._addSchema(e,t);return r.validate||this._compileSchemaEnv(r)}compileAsync(e,t){if(typeof this.opts.loadSchema!="function"){throw new Error("options.loadSchema should be a function")}const{loadSchema:r}=this.opts;return s.call(this,e,t);async function s(e,t){await n.call(this,e.$schema);const r=this._addSchema(e,t);return r.validate||o.call(this,r)}async function n(e){if(e&&!this.getSchema(e)){await s.call(this,{$ref:e},true)}}async function o(e){try{return this._compileSchemaEnv(e)}catch(t){if(!(t instanceof a.default))throw t;i.call(this,t);await c.call(this,t.missingSchema);return o.call(this,e)}}function i({missingSchema:e,missingRef:t}){if(this.refs[e]){throw new Error(`AnySchema ${e} is loaded but ${t} cannot be resolved`)}}async function c(e){const r=await u.call(this,e);if(!this.refs[e])await n.call(this,r.$schema);if(!this.refs[e])this.addSchema(r,e,t)}async function u(e){const t=this._loading[e];if(t)return t;try{return await(this._loading[e]=r(e))}finally{delete this._loading[e]}}}addSchema(e,t,r,s=this.opts.validateSchema){if(Array.isArray(e)){for(const t of e)this.addSchema(t,undefined,r,s);return this}let n;if(typeof e==="object"){const{schemaId:t}=this.opts;n=e[t];if(n!==undefined&&typeof n!="string"){throw new Error(`schema ${t} must be string`)}}t=(0,d.normalizeId)(t||n);this._checkUnique(t);this.schemas[t]=this._addSchema(e,r,t,s,true);return this}addMetaSchema(e,t,r=this.opts.validateSchema){this.addSchema(e,t,true,r);return this}validateSchema(e,t){if(typeof e=="boolean")return true;let r;r=e.$schema;if(r!==undefined&&typeof r!="string"){throw new Error("$schema must be a string")}r=r||this.opts.defaultMeta||this.defaultMeta();if(!r){this.logger.warn("meta-schema not available");this.errors=null;return true}const s=this.validate(r,e);if(!s&&t){const e="schema is invalid: "+this.errorsText();if(this.opts.validateSchema==="log")this.logger.error(e);else throw new Error(e)}return s}getSchema(e){let t;while(typeof(t=E.call(this,e))=="string")e=t;if(t===undefined){const{schemaId:r}=this.opts;const s=new c.SchemaEnv({schema:{},schemaId:r});t=c.resolveSchema.call(this,s,e);if(!t)return;this.refs[e]=t}return t.validate||this._compileSchemaEnv(t)}removeSchema(e){if(e instanceof RegExp){this._removeAllSchemas(this.schemas,e);this._removeAllSchemas(this.refs,e);return this}switch(typeof e){case"undefined":this._removeAllSchemas(this.schemas);this._removeAllSchemas(this.refs);this._cache.clear();return this;case"string":{const t=E.call(this,e);if(typeof t=="object")this._cache.delete(t.schema);delete this.schemas[e];delete this.refs[e];return this}case"object":{const t=e;this._cache.delete(t);let r=e[this.opts.schemaId];if(r){r=(0,d.normalizeId)(r);delete this.schemas[r];delete this.refs[r]}return this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(e){for(const t of e)this.addKeyword(t);return this}addKeyword(e,t){let r;if(typeof e=="string"){r=e;if(typeof t=="object"){this.logger.warn("these parameters are deprecated, see docs for addKeyword");t.keyword=r}}else if(typeof e=="object"&&t===undefined){t=e;r=t.keyword;if(Array.isArray(r)&&!r.length){throw new Error("addKeywords: keyword must be string or non-empty array")}}else{throw new Error("invalid addKeywords parameters")}x.call(this,r,t);if(!t){(0,f.eachItem)(r,(e=>T.call(this,e)));return this}M.call(this,t);const s={...t,type:(0,l.getJSONTypes)(t.type),schemaType:(0,l.getJSONTypes)(t.schemaType)};(0,f.eachItem)(r,s.type.length===0?e=>T.call(this,e,s):e=>s.type.forEach((t=>T.call(this,e,s,t))));return this}getKeyword(e){const t=this.RULES.all[e];return typeof t=="object"?t.definition:!!t}removeKeyword(e){const{RULES:t}=this;delete t.keywords[e];delete t.all[e];for(const r of t.rules){const t=r.rules.findIndex((t=>t.keyword===e));if(t>=0)r.rules.splice(t,1)}return this}addFormat(e,t){if(typeof t=="string")t=new RegExp(t);this.formats[e]=t;return this}errorsText(e=this.errors,{separator:t=", ",dataVar:r="data"}={}){if(!e||e.length===0)return"No errors";return e.map((e=>`${r}${e.instancePath} ${e.message}`)).reduce(((e,r)=>e+t+r))}$dataMetaSchema(e,t){const r=this.RULES.all;e=JSON.parse(JSON.stringify(e));for(const s of t){const t=s.split("/").slice(1);let n=e;for(const e of t)n=n[e];for(const e in r){const t=r[e];if(typeof t!="object")continue;const{$data:s}=t.definition;const o=n[e];if(s&&o)n[e]=D(o)}}return e}_removeAllSchemas(e,t){for(const r in e){const s=e[r];if(!t||t.test(r)){if(typeof s=="string"){delete e[r]}else if(s&&!s.meta){this._cache.delete(s.schema);delete e[r]}}}}_addSchema(e,t,r,s=this.opts.validateSchema,n=this.opts.addUsedSchema){let o;const{schemaId:a}=this.opts;if(typeof e=="object"){o=e[a]}else{if(this.opts.jtd)throw new Error("schema must be object");else if(typeof e!="boolean")throw new Error("schema must be object or boolean")}let i=this._cache.get(e);if(i!==undefined)return i;r=(0,d.normalizeId)(o||r);const u=d.getSchemaRefs.call(this,e,r);i=new c.SchemaEnv({schema:e,schemaId:a,meta:t,baseId:r,localRefs:u});this._cache.set(i.schema,i);if(n&&!r.startsWith("#")){if(r)this._checkUnique(r);this.refs[r]=i}if(s)this.validateSchema(e,true);return i}_checkUnique(e){if(this.schemas[e]||this.refs[e]){throw new Error(`schema with key or id "${e}" already exists`)}}_compileSchemaEnv(e){if(e.meta)this._compileMetaSchema(e);else c.compileSchema.call(this,e);if(!e.validate)throw new Error("ajv implementation error");return e.validate}_compileMetaSchema(e){const t=this.opts;this.opts=this._metaOpts;try{c.compileSchema.call(this,e)}finally{this.opts=t}}}b.ValidationError=o.default;b.MissingRefError=a.default;t["default"]=b;function P(e,t,r,s="error"){for(const n in e){const o=n;if(o in t)this.logger[s](`${r}: option ${n}. ${e[o]}`)}}function E(e){e=(0,d.normalizeId)(e);return this.schemas[e]||this.refs[e]}function S(){const e=this.opts.schemas;if(!e)return;if(Array.isArray(e))this.addSchema(e);else for(const t in e)this.addSchema(e[t],t)}function k(){for(const e in this.opts.formats){const t=this.opts.formats[e];if(t)this.addFormat(e,t)}}function N(e){if(Array.isArray(e)){this.addVocabulary(e);return}this.logger.warn("keywords option as map is deprecated, pass array");for(const t in e){const r=e[t];if(!r.keyword)r.keyword=t;this.addKeyword(r)}}function j(){const e={...this.opts};for(const t of y)delete e[t];return e}const O={log(){},warn(){},error(){}};function C(e){if(e===false)return O;if(e===undefined)return console;if(e.log&&e.warn&&e.error)return e;throw new Error("logger must implement log, warn and error methods")}const I=/^[a-z_$][a-z0-9_$:-]*$/i;function x(e,t){const{RULES:r}=this;(0,f.eachItem)(e,(e=>{if(r.keywords[e])throw new Error(`Keyword ${e} is already defined`);if(!I.test(e))throw new Error(`Keyword ${e} has invalid name`)}));if(!t)return;if(t.$data&&!("code"in t||"validate"in t)){throw new Error('$data keyword must have "code" or "validate" function')}}function T(e,t,r){var s;const n=t===null||t===void 0?void 0:t.post;if(r&&n)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:o}=this;let a=n?o.post:o.rules.find((({type:e})=>e===r));if(!a){a={type:r,rules:[]};o.rules.push(a)}o.keywords[e]=true;if(!t)return;const i={keyword:e,definition:{...t,type:(0,l.getJSONTypes)(t.type),schemaType:(0,l.getJSONTypes)(t.schemaType)}};if(t.before)R.call(this,a,i,t.before);else a.rules.push(i);o.all[e]=i;(s=t.implements)===null||s===void 0?void 0:s.forEach((e=>this.addKeyword(e)))}function R(e,t,r){const s=e.rules.findIndex((e=>e.keyword===r));if(s>=0){e.rules.splice(s,0,t)}else{e.rules.push(t);this.logger.warn(`rule ${r} is not defined`)}}function M(e){let{metaSchema:t}=e;if(t===undefined)return;if(e.$data&&this.opts.$data)t=D(t);e.validateSchema=this.compile(t,true)}const A={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function D(e){return{anyOf:[e,A]}}},76250:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(32017);s.code='require("ajv/dist/runtime/equal").default';t["default"]=s},53853:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});function r(e){const t=e.length;let r=0;let s=0;let n;while(s<t){r++;n=e.charCodeAt(s++);if(n>=55296&&n<=56319&&s<t){n=e.charCodeAt(s);if((n&64512)===56320)s++}}return r}t["default"]=r;r.code='require("ajv/dist/runtime/ucs2length").default'},55944:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(74488);s.code='require("ajv/dist/runtime/uri").default';t["default"]=s},13558:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});class r extends Error{constructor(e){super("validation failed");this.errors=e;this.ajv=this.validation=true}}t["default"]=r},15457:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.validateAdditionalItems=void 0;const s=r(99029);const n=r(94227);const o={message:({params:{len:e}})=>(0,s.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,s._)`{limit: ${e}}`};const a={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:o,code(e){const{parentSchema:t,it:r}=e;const{items:s}=t;if(!Array.isArray(s)){(0,n.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas');return}i(e,s)}};function i(e,t){const{gen:r,schema:o,data:a,keyword:i,it:c}=e;c.items=true;const u=r.const("len",(0,s._)`${a}.length`);if(o===false){e.setParams({len:t.length});e.pass((0,s._)`${u} <= ${t.length}`)}else if(typeof o=="object"&&!(0,n.alwaysValidSchema)(c,o)){const n=r.var("valid",(0,s._)`${u} <= ${t.length}`);r.if((0,s.not)(n),(()=>d(n)));e.ok(n)}function d(o){r.forRange("i",t.length,u,(t=>{e.subschema({keyword:i,dataProp:t,dataPropType:n.Type.Num},o);if(!c.allErrors)r.if((0,s.not)(o),(()=>r.break()))}))}}t.validateAdditionalItems=i;t["default"]=a},38660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(15765);const n=r(99029);const o=r(42023);const a=r(94227);const i={message:"must NOT have additional properties",params:({params:e})=>(0,n._)`{additionalProperty: ${e.additionalProperty}}`};const c={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:true,trackErrors:true,error:i,code(e){const{gen:t,schema:r,parentSchema:i,data:c,errsCount:u,it:d}=e;if(!u)throw new Error("ajv implementation error");const{allErrors:l,opts:f}=d;d.props=true;if(f.removeAdditional!=="all"&&(0,a.alwaysValidSchema)(d,r))return;const h=(0,s.allSchemaProperties)(i.properties);const p=(0,s.allSchemaProperties)(i.patternProperties);m();e.ok((0,n._)`${u} === ${o.default.errors}`);function m(){t.forIn("key",c,(e=>{if(!h.length&&!p.length)$(e);else t.if(y(e),(()=>$(e)))}))}function y(r){let o;if(h.length>8){const e=(0,a.schemaRefOrVal)(d,i.properties,"properties");o=(0,s.isOwnProperty)(t,e,r)}else if(h.length){o=(0,n.or)(...h.map((e=>(0,n._)`${r} === ${e}`)))}else{o=n.nil}if(p.length){o=(0,n.or)(o,...p.map((t=>(0,n._)`${(0,s.usePattern)(e,t)}.test(${r})`)))}return(0,n.not)(o)}function g(e){t.code((0,n._)`delete ${c}[${e}]`)}function $(s){if(f.removeAdditional==="all"||f.removeAdditional&&r===false){g(s);return}if(r===false){e.setParams({additionalProperty:s});e.error();if(!l)t.break();return}if(typeof r=="object"&&!(0,a.alwaysValidSchema)(d,r)){const r=t.name("valid");if(f.removeAdditional==="failing"){v(s,r,false);t.if((0,n.not)(r),(()=>{e.reset();g(s)}))}else{v(s,r);if(!l)t.if((0,n.not)(r),(()=>t.break()))}}}function v(t,r,s){const n={keyword:"additionalProperties",dataProp:t,dataPropType:a.Type.Str};if(s===false){Object.assign(n,{compositeRule:true,createErrors:false,allErrors:false})}e.subschema(n,r)}}};t["default"]=c},15844:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(94227);const n={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const o=t.name("valid");r.forEach(((t,r)=>{if((0,s.alwaysValidSchema)(n,t))return;const a=e.subschema({keyword:"allOf",schemaProp:r},o);e.ok(o);e.mergeEvaluated(a)}))}};t["default"]=n},16505:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(15765);const n={keyword:"anyOf",schemaType:"array",trackErrors:true,code:s.validateUnion,error:{message:"must match a schema in anyOf"}};t["default"]=n},12661:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=r(94227);const o={message:({params:{min:e,max:t}})=>t===undefined?(0,s.str)`must contain at least ${e} valid item(s)`:(0,s.str)`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>t===undefined?(0,s._)`{minContains: ${e}}`:(0,s._)`{minContains: ${e}, maxContains: ${t}}`};const a={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:true,error:o,code(e){const{gen:t,schema:r,parentSchema:o,data:a,it:i}=e;let c;let u;const{minContains:d,maxContains:l}=o;if(i.opts.next){c=d===undefined?1:d;u=l}else{c=1}const f=t.const("len",(0,s._)`${a}.length`);e.setParams({min:c,max:u});if(u===undefined&&c===0){(0,n.checkStrictMode)(i,`"minContains" == 0 without "maxContains": "contains" keyword ignored`);return}if(u!==undefined&&c>u){(0,n.checkStrictMode)(i,`"minContains" > "maxContains" is always invalid`);e.fail();return}if((0,n.alwaysValidSchema)(i,r)){let t=(0,s._)`${f} >= ${c}`;if(u!==undefined)t=(0,s._)`${t} && ${f} <= ${u}`;e.pass(t);return}i.items=true;const h=t.name("valid");if(u===undefined&&c===1){m(h,(()=>t.if(h,(()=>t.break()))))}else if(c===0){t.let(h,true);if(u!==undefined)t.if((0,s._)`${a}.length > 0`,p)}else{t.let(h,false);p()}e.result(h,(()=>e.reset()));function p(){const e=t.name("_valid");const r=t.let("count",0);m(e,(()=>t.if(e,(()=>y(r)))))}function m(r,s){t.forRange("i",0,f,(t=>{e.subschema({keyword:"contains",dataProp:t,dataPropType:n.Type.Num,compositeRule:true},r);s()}))}function y(e){t.code((0,s._)`${e}++`);if(u===undefined){t.if((0,s._)`${e} >= ${c}`,(()=>t.assign(h,true).break()))}else{t.if((0,s._)`${e} > ${u}`,(()=>t.assign(h,false).break()));if(c===1)t.assign(h,true);else t.if((0,s._)`${e} >= ${c}`,(()=>t.assign(h,true)))}}}};t["default"]=a},83025:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.validateSchemaDeps=t.validatePropertyDeps=t.error=void 0;const s=r(99029);const n=r(94227);const o=r(15765);t.error={message:({params:{property:e,depsCount:t,deps:r}})=>{const n=t===1?"property":"properties";return(0,s.str)`must have ${n} ${r} when property ${e} is present`},params:({params:{property:e,depsCount:t,deps:r,missingProperty:n}})=>(0,s._)`{property: ${e},
    missingProperty: ${n},
    depsCount: ${t},
    deps: ${r}}`};const a={keyword:"dependencies",type:"object",schemaType:"object",error:t.error,code(e){const[t,r]=i(e);c(e,t);u(e,r)}};function i({schema:e}){const t={};const r={};for(const s in e){if(s==="__proto__")continue;const n=Array.isArray(e[s])?t:r;n[s]=e[s]}return[t,r]}function c(e,t=e.schema){const{gen:r,data:n,it:a}=e;if(Object.keys(t).length===0)return;const i=r.let("missing");for(const c in t){const u=t[c];if(u.length===0)continue;const d=(0,o.propertyInData)(r,n,c,a.opts.ownProperties);e.setParams({property:c,depsCount:u.length,deps:u.join(", ")});if(a.allErrors){r.if(d,(()=>{for(const t of u){(0,o.checkReportMissingProp)(e,t)}}))}else{r.if((0,s._)`${d} && (${(0,o.checkMissingProp)(e,u,i)})`);(0,o.reportMissingProp)(e,i);r.else()}}}t.validatePropertyDeps=c;function u(e,t=e.schema){const{gen:r,data:s,keyword:a,it:i}=e;const c=r.name("valid");for(const u in t){if((0,n.alwaysValidSchema)(i,t[u]))continue;r.if((0,o.propertyInData)(r,s,u,i.opts.ownProperties),(()=>{const t=e.subschema({keyword:a,schemaProp:u},c);e.mergeValidEvaluated(t,c)}),(()=>r.var(c,true)));e.ok(c)}}t.validateSchemaDeps=u;t["default"]=a},1239:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=r(94227);const o={message:({params:e})=>(0,s.str)`must match "${e.ifClause}" schema`,params:({params:e})=>(0,s._)`{failingKeyword: ${e.ifClause}}`};const a={keyword:"if",schemaType:["object","boolean"],trackErrors:true,error:o,code(e){const{gen:t,parentSchema:r,it:o}=e;if(r.then===undefined&&r.else===undefined){(0,n.checkStrictMode)(o,'"if" without "then" and "else" is ignored')}const a=i(o,"then");const c=i(o,"else");if(!a&&!c)return;const u=t.let("valid",true);const d=t.name("_valid");l();e.reset();if(a&&c){const r=t.let("ifClause");e.setParams({ifClause:r});t.if(d,f("then",r),f("else",r))}else if(a){t.if(d,f("then"))}else{t.if((0,s.not)(d),f("else"))}e.pass(u,(()=>e.error(true)));function l(){const t=e.subschema({keyword:"if",compositeRule:true,createErrors:false,allErrors:false},d);e.mergeEvaluated(t)}function f(r,n){return()=>{const o=e.subschema({keyword:r},d);t.assign(u,d);e.mergeValidEvaluated(o,u);if(n)t.assign(n,(0,s._)`${r}`);else e.setParams({ifClause:r})}}}};function i(e,t){const r=e.schema[t];return r!==undefined&&!(0,n.alwaysValidSchema)(e,r)}t["default"]=a},56378:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(15457);const n=r(65354);const o=r(20494);const a=r(93966);const i=r(12661);const c=r(83025);const u=r(19713);const d=r(38660);const l=r(40117);const f=r(45333);const h=r(57923);const p=r(16505);const m=r(96163);const y=r(15844);const g=r(1239);const $=r(14426);function v(e=false){const t=[h.default,p.default,m.default,y.default,g.default,$.default,u.default,d.default,c.default,l.default,f.default];if(e)t.push(n.default,a.default);else t.push(s.default,o.default);t.push(i.default);return t}t["default"]=v},20494:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.validateTuple=void 0;const s=r(99029);const n=r(94227);const o=r(15765);const a={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return i(e,"additionalItems",t);r.items=true;if((0,n.alwaysValidSchema)(r,t))return;e.ok((0,o.validateArray)(e))}};function i(e,t,r=e.schema){const{gen:o,parentSchema:a,data:i,keyword:c,it:u}=e;f(a);if(u.opts.unevaluated&&r.length&&u.items!==true){u.items=n.mergeEvaluated.items(o,r.length,u.items)}const d=o.name("valid");const l=o.const("len",(0,s._)`${i}.length`);r.forEach(((t,r)=>{if((0,n.alwaysValidSchema)(u,t))return;o.if((0,s._)`${l} > ${r}`,(()=>e.subschema({keyword:c,schemaProp:r,dataProp:r},d)));e.ok(d)}));function f(e){const{opts:s,errSchemaPath:o}=u;const a=r.length;const i=a===e.minItems&&(a===e.maxItems||e[t]===false);if(s.strictTuples&&!i){const e=`"${c}" is ${a}-tuple, but minItems or maxItems/${t} are not specified or different at path "${o}"`;(0,n.checkStrictMode)(u,e,s.strictTuples)}}}t.validateTuple=i;t["default"]=a},93966:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=r(94227);const o=r(15765);const a=r(15457);const i={message:({params:{len:e}})=>(0,s.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,s._)`{limit: ${e}}`};const c={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:i,code(e){const{schema:t,parentSchema:r,it:s}=e;const{prefixItems:i}=r;s.items=true;if((0,n.alwaysValidSchema)(s,t))return;if(i)(0,a.validateAdditionalItems)(e,i);else e.ok((0,o.validateArray)(e))}};t["default"]=c},57923:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(94227);const n={keyword:"not",schemaType:["object","boolean"],trackErrors:true,code(e){const{gen:t,schema:r,it:n}=e;if((0,s.alwaysValidSchema)(n,r)){e.fail();return}const o=t.name("valid");e.subschema({keyword:"not",compositeRule:true,createErrors:false,allErrors:false},o);e.failResult(o,(()=>e.reset()),(()=>e.error()))},error:{message:"must NOT be valid"}};t["default"]=n},96163:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=r(94227);const o={message:"must match exactly one schema in oneOf",params:({params:e})=>(0,s._)`{passingSchemas: ${e.passing}}`};const a={keyword:"oneOf",schemaType:"array",trackErrors:true,error:o,code(e){const{gen:t,schema:r,parentSchema:o,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(a.opts.discriminator&&o.discriminator)return;const i=r;const c=t.let("valid",false);const u=t.let("passing",null);const d=t.name("_valid");e.setParams({passing:u});t.block(l);e.result(c,(()=>e.reset()),(()=>e.error(true)));function l(){i.forEach(((r,o)=>{let i;if((0,n.alwaysValidSchema)(a,r)){t.var(d,true)}else{i=e.subschema({keyword:"oneOf",schemaProp:o,compositeRule:true},d)}if(o>0){t.if((0,s._)`${d} && ${c}`).assign(c,false).assign(u,(0,s._)`[${u}, ${o}]`).else()}t.if(d,(()=>{t.assign(c,true);t.assign(u,o);if(i)e.mergeEvaluated(i,s.Name)}))}))}}};t["default"]=a},45333:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(15765);const n=r(99029);const o=r(94227);const a=r(94227);const i={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:i,parentSchema:c,it:u}=e;const{opts:d}=u;const l=(0,s.allSchemaProperties)(r);const f=l.filter((e=>(0,o.alwaysValidSchema)(u,r[e])));if(l.length===0||f.length===l.length&&(!u.opts.unevaluated||u.props===true)){return}const h=d.strictSchema&&!d.allowMatchingProperties&&c.properties;const p=t.name("valid");if(u.props!==true&&!(u.props instanceof n.Name)){u.props=(0,a.evaluatedPropsToName)(t,u.props)}const{props:m}=u;y();function y(){for(const e of l){if(h)g(e);if(u.allErrors){$(e)}else{t.var(p,true);$(e);t.if(p)}}}function g(e){for(const t in h){if(new RegExp(e).test(t)){(0,o.checkStrictMode)(u,`property ${t} matches pattern ${e} (use allowMatchingProperties)`)}}}function $(r){t.forIn("key",i,(o=>{t.if((0,n._)`${(0,s.usePattern)(e,r)}.test(${o})`,(()=>{const s=f.includes(r);if(!s){e.subschema({keyword:"patternProperties",schemaProp:r,dataProp:o,dataPropType:a.Type.Str},p)}if(u.opts.unevaluated&&m!==true){t.assign((0,n._)`${m}[${o}]`,true)}else if(!s&&!u.allErrors){t.if((0,n.not)(p),(()=>t.break()))}}))}))}}};t["default"]=i},65354:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(20494);const n={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,s.validateTuple)(e,"items")};t["default"]=n},40117:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(62586);const n=r(15765);const o=r(94227);const a=r(38660);const i={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:i,data:c,it:u}=e;if(u.opts.removeAdditional==="all"&&i.additionalProperties===undefined){a.default.code(new s.KeywordCxt(u,a.default,"additionalProperties"))}const d=(0,n.allSchemaProperties)(r);for(const s of d){u.definedProperties.add(s)}if(u.opts.unevaluated&&d.length&&u.props!==true){u.props=o.mergeEvaluated.props(t,(0,o.toHash)(d),u.props)}const l=d.filter((e=>!(0,o.alwaysValidSchema)(u,r[e])));if(l.length===0)return;const f=t.name("valid");for(const s of l){if(h(s)){p(s)}else{t.if((0,n.propertyInData)(t,c,s,u.opts.ownProperties));p(s);if(!u.allErrors)t.else().var(f,true);t.endIf()}e.it.definedProperties.add(s);e.ok(f)}function h(e){return u.opts.useDefaults&&!u.compositeRule&&r[e].default!==undefined}function p(t){e.subschema({keyword:"properties",schemaProp:t,dataProp:t},f)}}};t["default"]=i},19713:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=r(94227);const o={message:"property name must be valid",params:({params:e})=>(0,s._)`{propertyName: ${e.propertyName}}`};const a={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:o,code(e){const{gen:t,schema:r,data:o,it:a}=e;if((0,n.alwaysValidSchema)(a,r))return;const i=t.name("valid");t.forIn("key",o,(r=>{e.setParams({propertyName:r});e.subschema({keyword:"propertyNames",data:r,dataTypes:["string"],propertyName:r,compositeRule:true},i);t.if((0,s.not)(i),(()=>{e.error(true);if(!a.allErrors)t.break()}))}));e.ok(i)}};t["default"]=a},14426:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(94227);const n={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){if(t.if===undefined)(0,s.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};t["default"]=n},15765:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.validateUnion=t.validateArray=t.usePattern=t.callValidateCode=t.schemaProperties=t.allSchemaProperties=t.noPropertyInData=t.propertyInData=t.isOwnProperty=t.hasPropFunc=t.reportMissingProp=t.checkMissingProp=t.checkReportMissingProp=void 0;const s=r(99029);const n=r(94227);const o=r(42023);const a=r(94227);function i(e,t){const{gen:r,data:n,it:o}=e;r.if(h(r,n,t,o.opts.ownProperties),(()=>{e.setParams({missingProperty:(0,s._)`${t}`},true);e.error()}))}t.checkReportMissingProp=i;function c({gen:e,data:t,it:{opts:r}},n,o){return(0,s.or)(...n.map((n=>(0,s.and)(h(e,t,n,r.ownProperties),(0,s._)`${o} = ${n}`))))}t.checkMissingProp=c;function u(e,t){e.setParams({missingProperty:t},true);e.error()}t.reportMissingProp=u;function d(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:(0,s._)`Object.prototype.hasOwnProperty`})}t.hasPropFunc=d;function l(e,t,r){return(0,s._)`${d(e)}.call(${t}, ${r})`}t.isOwnProperty=l;function f(e,t,r,n){const o=(0,s._)`${t}${(0,s.getProperty)(r)} !== undefined`;return n?(0,s._)`${o} && ${l(e,t,r)}`:o}t.propertyInData=f;function h(e,t,r,n){const o=(0,s._)`${t}${(0,s.getProperty)(r)} === undefined`;return n?(0,s.or)(o,(0,s.not)(l(e,t,r))):o}t.noPropertyInData=h;function p(e){return e?Object.keys(e).filter((e=>e!=="__proto__")):[]}t.allSchemaProperties=p;function m(e,t){return p(t).filter((r=>!(0,n.alwaysValidSchema)(e,t[r])))}t.schemaProperties=m;function y({schemaCode:e,data:t,it:{gen:r,topSchemaRef:n,schemaPath:a,errorPath:i},it:c},u,d,l){const f=l?(0,s._)`${e}, ${t}, ${n}${a}`:t;const h=[[o.default.instancePath,(0,s.strConcat)(o.default.instancePath,i)],[o.default.parentData,c.parentData],[o.default.parentDataProperty,c.parentDataProperty],[o.default.rootData,o.default.rootData]];if(c.opts.dynamicRef)h.push([o.default.dynamicAnchors,o.default.dynamicAnchors]);const p=(0,s._)`${f}, ${r.object(...h)}`;return d!==s.nil?(0,s._)`${u}.call(${d}, ${p})`:(0,s._)`${u}(${p})`}t.callValidateCode=y;const g=(0,s._)`new RegExp`;function $({gen:e,it:{opts:t}},r){const n=t.unicodeRegExp?"u":"";const{regExp:o}=t.code;const i=o(r,n);return e.scopeValue("pattern",{key:i.toString(),ref:i,code:(0,s._)`${o.code==="new RegExp"?g:(0,a.useFunc)(e,o)}(${r}, ${n})`})}t.usePattern=$;function v(e){const{gen:t,data:r,keyword:o,it:a}=e;const i=t.name("valid");if(a.allErrors){const e=t.let("valid",true);c((()=>t.assign(e,false)));return e}t.var(i,true);c((()=>t.break()));return i;function c(a){const c=t.const("len",(0,s._)`${r}.length`);t.forRange("i",0,c,(r=>{e.subschema({keyword:o,dataProp:r,dataPropType:n.Type.Num},i);t.if((0,s.not)(i),a)}))}}t.validateArray=v;function _(e){const{gen:t,schema:r,keyword:o,it:a}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const i=r.some((e=>(0,n.alwaysValidSchema)(a,e)));if(i&&!a.opts.unevaluated)return;const c=t.let("valid",false);const u=t.name("_valid");t.block((()=>r.forEach(((r,n)=>{const a=e.subschema({keyword:o,schemaProp:n,compositeRule:true},u);t.assign(c,(0,s._)`${c} || ${u}`);const i=e.mergeValidEvaluated(a,u);if(!i)t.if((0,s.not)(c))}))));e.result(c,(()=>e.reset()),(()=>e.error(true)))}t.validateUnion=_},83463:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});const r={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};t["default"]=r},72128:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(83463);const n=r(13693);const o=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",s.default,n.default];t["default"]=o},13693:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});t.callRef=t.getValidate=void 0;const s=r(34551);const n=r(15765);const o=r(99029);const a=r(42023);const i=r(73835);const c=r(94227);const u={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:n}=e;const{baseId:a,schemaEnv:c,validateName:u,opts:f,self:h}=n;const{root:p}=c;if((r==="#"||r==="#/")&&a===p.baseId)return y();const m=i.resolveRef.call(h,p,a,r);if(m===undefined)throw new s.default(n.opts.uriResolver,a,r);if(m instanceof i.SchemaEnv)return g(m);return $(m);function y(){if(c===p)return l(e,u,c,c.$async);const r=t.scopeValue("root",{ref:p});return l(e,(0,o._)`${r}.validate`,p,p.$async)}function g(t){const r=d(e,t);l(e,r,t,t.$async)}function $(s){const n=t.scopeValue("schema",f.code.source===true?{ref:s,code:(0,o.stringify)(s)}:{ref:s});const a=t.name("valid");const i=e.subschema({schema:s,dataTypes:[],schemaPath:o.nil,topSchemaRef:n,errSchemaPath:r},a);e.mergeEvaluated(i);e.ok(a)}}};function d(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):(0,o._)`${r.scopeValue("wrapper",{ref:t})}.validate`}t.getValidate=d;function l(e,t,r,s){const{gen:i,it:u}=e;const{allErrors:d,schemaEnv:l,opts:f}=u;const h=f.passContext?a.default.this:o.nil;if(s)p();else m();function p(){if(!l.$async)throw new Error("async schema referenced by sync schema");const r=i.let("valid");i.try((()=>{i.code((0,o._)`await ${(0,n.callValidateCode)(e,t,h)}`);g(t);if(!d)i.assign(r,true)}),(e=>{i.if((0,o._)`!(${e} instanceof ${u.ValidationError})`,(()=>i.throw(e)));y(e);if(!d)i.assign(r,false)}));e.ok(r)}function m(){e.result((0,n.callValidateCode)(e,t,h),(()=>g(t)),(()=>y(t)))}function y(e){const t=(0,o._)`${e}.errors`;i.assign(a.default.vErrors,(0,o._)`${a.default.vErrors} === null ? ${t} : ${a.default.vErrors}.concat(${t})`);i.assign(a.default.errors,(0,o._)`${a.default.vErrors}.length`)}function g(e){var t;if(!u.opts.unevaluated)return;const s=(t=r===null||r===void 0?void 0:r.validate)===null||t===void 0?void 0:t.evaluated;if(u.props!==true){if(s&&!s.dynamicProps){if(s.props!==undefined){u.props=c.mergeEvaluated.props(i,s.props,u.props)}}else{const t=i.var("props",(0,o._)`${e}.evaluated.props`);u.props=c.mergeEvaluated.props(i,t,u.props,o.Name)}}if(u.items!==true){if(s&&!s.dynamicItems){if(s.items!==undefined){u.items=c.mergeEvaluated.items(i,s.items,u.items)}}else{const t=i.var("items",(0,o._)`${e}.evaluated.items`);u.items=c.mergeEvaluated.items(i,t,u.items,o.Name)}}}}t.callRef=l;t["default"]=u},36653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=r(97652);const o=r(73835);const a=r(34551);const i=r(94227);const c={message:({params:{discrError:e,tagName:t}})=>e===n.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>(0,s._)`{error: ${e}, tag: ${r}, tagValue: ${t}}`};const u={keyword:"discriminator",type:"object",schemaType:"object",error:c,code(e){const{gen:t,data:r,schema:c,parentSchema:u,it:d}=e;const{oneOf:l}=u;if(!d.opts.discriminator){throw new Error("discriminator: requires discriminator option")}const f=c.propertyName;if(typeof f!="string")throw new Error("discriminator: requires propertyName");if(c.mapping)throw new Error("discriminator: mapping is not supported");if(!l)throw new Error("discriminator: requires oneOf keyword");const h=t.let("valid",false);const p=t.const("tag",(0,s._)`${r}${(0,s.getProperty)(f)}`);t.if((0,s._)`typeof ${p} == "string"`,(()=>m()),(()=>e.error(false,{discrError:n.DiscrError.Tag,tag:p,tagName:f})));e.ok(h);function m(){const r=g();t.if(false);for(const e in r){t.elseIf((0,s._)`${p} === ${e}`);t.assign(h,y(r[e]))}t.else();e.error(false,{discrError:n.DiscrError.Mapping,tag:p,tagName:f});t.endIf()}function y(r){const n=t.name("valid");const o=e.subschema({keyword:"oneOf",schemaProp:r},n);e.mergeEvaluated(o,s.Name);return n}function g(){var e;const t={};const r=n(u);let s=true;for(let u=0;u<l.length;u++){let t=l[u];if((t===null||t===void 0?void 0:t.$ref)&&!(0,i.schemaHasRulesButRef)(t,d.self.RULES)){const e=t.$ref;t=o.resolveRef.call(d.self,d.schemaEnv.root,d.baseId,e);if(t instanceof o.SchemaEnv)t=t.schema;if(t===undefined)throw new a.default(d.opts.uriResolver,d.baseId,e)}const h=(e=t===null||t===void 0?void 0:t.properties)===null||e===void 0?void 0:e[f];if(typeof h!="object"){throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${f}"`)}s=s&&(r||n(t));c(h,u)}if(!s)throw new Error(`discriminator: "${f}" must be required`);return t;function n({required:e}){return Array.isArray(e)&&e.includes(f)}function c(e,t){if(e.const){h(e.const,t)}else if(e.enum){for(const r of e.enum){h(r,t)}}else{throw new Error(`discriminator: "properties/${f}" must have "const" or "enum"`)}}function h(e,r){if(typeof e!="string"||e in t){throw new Error(`discriminator: "${f}" values must be unique strings`)}t[e]=r}}}};t["default"]=u},97652:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});t.DiscrError=void 0;var r;(function(e){e["Tag"]="tag";e["Mapping"]="mapping"})(r||(t.DiscrError=r={}))},86144:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(72128);const n=r(67060);const o=r(56378);const a=r(97532);const i=r(69857);const c=[s.default,n.default,(0,o.default)(),a.default,i.metadataVocabulary,i.contentVocabulary];t["default"]=c},94737:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n={message:({schemaCode:e})=>(0,s.str)`must match format "${e}"`,params:({schemaCode:e})=>(0,s._)`{format: ${e}}`};const o={keyword:"format",type:["number","string"],schemaType:"string",$data:true,error:n,code(e,t){const{gen:r,data:n,$data:o,schema:a,schemaCode:i,it:c}=e;const{opts:u,errSchemaPath:d,schemaEnv:l,self:f}=c;if(!u.validateFormats)return;if(o)h();else p();function h(){const o=r.scopeValue("formats",{ref:f.formats,code:u.code.formats});const a=r.const("fDef",(0,s._)`${o}[${i}]`);const c=r.let("fType");const d=r.let("format");r.if((0,s._)`typeof ${a} == "object" && !(${a} instanceof RegExp)`,(()=>r.assign(c,(0,s._)`${a}.type || "string"`).assign(d,(0,s._)`${a}.validate`)),(()=>r.assign(c,(0,s._)`"string"`).assign(d,a)));e.fail$data((0,s.or)(h(),p()));function h(){if(u.strictSchema===false)return s.nil;return(0,s._)`${i} && !${d}`}function p(){const e=l.$async?(0,s._)`(${a}.async ? await ${d}(${n}) : ${d}(${n}))`:(0,s._)`${d}(${n})`;const r=(0,s._)`(typeof ${d} == "function" ? ${e} : ${d}.test(${n}))`;return(0,s._)`${d} && ${d} !== true && ${c} === ${t} && !${r}`}}function p(){const o=f.formats[a];if(!o){p();return}if(o===true)return;const[i,c,h]=m(o);if(i===t)e.pass(y());function p(){if(u.strictSchema===false){f.logger.warn(e());return}throw new Error(e());function e(){return`unknown format "${a}" ignored in schema at path "${d}"`}}function m(e){const t=e instanceof RegExp?(0,s.regexpCode)(e):u.code.formats?(0,s._)`${u.code.formats}${(0,s.getProperty)(a)}`:undefined;const n=r.scopeValue("formats",{key:a,ref:e,code:t});if(typeof e=="object"&&!(e instanceof RegExp)){return[e.type||"string",e.validate,(0,s._)`${n}.validate`]}return["string",e,n]}function y(){if(typeof o=="object"&&!(o instanceof RegExp)&&o.async){if(!l.$async)throw new Error("async format in sync schema");return(0,s._)`await ${h}(${n})`}return typeof c=="function"?(0,s._)`${h}(${n})`:(0,s._)`${h}.test(${n})`}}}};t["default"]=o},97532:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(94737);const n=[s.default];t["default"]=n},69857:(e,t)=>{Object.defineProperty(t,"__esModule",{value:true});t.contentVocabulary=t.metadataVocabulary=void 0;t.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"];t.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"]},27935:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=r(94227);const o=r(76250);const a={message:"must be equal to constant",params:({schemaCode:e})=>(0,s._)`{allowedValue: ${e}}`};const i={keyword:"const",$data:true,error:a,code(e){const{gen:t,data:r,$data:a,schemaCode:i,schema:c}=e;if(a||c&&typeof c=="object"){e.fail$data((0,s._)`!${(0,n.useFunc)(t,o.default)}(${r}, ${i})`)}else{e.fail((0,s._)`${c} !== ${r}`)}}};t["default"]=i},28643:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=r(94227);const o=r(76250);const a={message:"must be equal to one of the allowed values",params:({schemaCode:e})=>(0,s._)`{allowedValues: ${e}}`};const i={keyword:"enum",schemaType:"array",$data:true,error:a,code(e){const{gen:t,data:r,$data:a,schema:i,schemaCode:c,it:u}=e;if(!a&&i.length===0)throw new Error("enum must have non-empty array");const d=i.length>=u.opts.loopEnum;let l;const f=()=>l!==null&&l!==void 0?l:l=(0,n.useFunc)(t,o.default);let h;if(d||a){h=t.let("valid");e.block$data(h,p)}else{if(!Array.isArray(i))throw new Error("ajv implementation error");const e=t.const("vSchema",c);h=(0,s.or)(...i.map(((t,r)=>m(e,r))))}e.pass(h);function p(){t.assign(h,false);t.forOf("v",c,(e=>t.if((0,s._)`${f()}(${r}, ${e})`,(()=>t.assign(h,true).break()))))}function m(e,t){const n=i[t];return typeof n==="object"&&n!==null?(0,s._)`${f()}(${r}, ${e}[${t}])`:(0,s._)`${r} === ${n}`}}};t["default"]=i},67060:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(75882);const n=r(63439);const o=r(77307);const a=r(90422);const i=r(34486);const c=r(34003);const u=r(61163);const d=r(60617);const l=r(27935);const f=r(28643);const h=[s.default,n.default,o.default,a.default,i.default,c.default,u.default,d.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},l.default,f.default];t["default"]=h},61163:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n={message({keyword:e,schemaCode:t}){const r=e==="maxItems"?"more":"fewer";return(0,s.str)`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>(0,s._)`{limit: ${e}}`};const o={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:true,error:n,code(e){const{keyword:t,data:r,schemaCode:n}=e;const o=t==="maxItems"?s.operators.GT:s.operators.LT;e.fail$data((0,s._)`${r}.length ${o} ${n}`)}};t["default"]=o},77307:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=r(94227);const o=r(53853);const a={message({keyword:e,schemaCode:t}){const r=e==="maxLength"?"more":"fewer";return(0,s.str)`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>(0,s._)`{limit: ${e}}`};const i={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:true,error:a,code(e){const{keyword:t,data:r,schemaCode:a,it:i}=e;const c=t==="maxLength"?s.operators.GT:s.operators.LT;const u=i.opts.unicode===false?(0,s._)`${r}.length`:(0,s._)`${(0,n.useFunc)(e.gen,o.default)}(${r})`;e.fail$data((0,s._)`${u} ${c} ${a}`)}};t["default"]=i},75882:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n=s.operators;const o={maximum:{okStr:"<=",ok:n.LTE,fail:n.GT},minimum:{okStr:">=",ok:n.GTE,fail:n.LT},exclusiveMaximum:{okStr:"<",ok:n.LT,fail:n.GTE},exclusiveMinimum:{okStr:">",ok:n.GT,fail:n.LTE}};const a={message:({keyword:e,schemaCode:t})=>(0,s.str)`must be ${o[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>(0,s._)`{comparison: ${o[e].okStr}, limit: ${t}}`};const i={keyword:Object.keys(o),type:"number",schemaType:"number",$data:true,error:a,code(e){const{keyword:t,data:r,schemaCode:n}=e;e.fail$data((0,s._)`${r} ${o[t].fail} ${n} || isNaN(${r})`)}};t["default"]=i},34486:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n={message({keyword:e,schemaCode:t}){const r=e==="maxProperties"?"more":"fewer";return(0,s.str)`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>(0,s._)`{limit: ${e}}`};const o={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:true,error:n,code(e){const{keyword:t,data:r,schemaCode:n}=e;const o=t==="maxProperties"?s.operators.GT:s.operators.LT;e.fail$data((0,s._)`Object.keys(${r}).length ${o} ${n}`)}};t["default"]=o},63439:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(99029);const n={message:({schemaCode:e})=>(0,s.str)`must be multiple of ${e}`,params:({schemaCode:e})=>(0,s._)`{multipleOf: ${e}}`};const o={keyword:"multipleOf",type:"number",schemaType:"number",$data:true,error:n,code(e){const{gen:t,data:r,schemaCode:n,it:o}=e;const a=o.opts.multipleOfPrecision;const i=t.let("res");const c=a?(0,s._)`Math.abs(Math.round(${i}) - ${i}) > 1e-${a}`:(0,s._)`${i} !== parseInt(${i})`;e.fail$data((0,s._)`(${n} === 0 || (${i} = ${r}/${n}, ${c}))`)}};t["default"]=o},90422:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(15765);const n=r(99029);const o={message:({schemaCode:e})=>(0,n.str)`must match pattern "${e}"`,params:({schemaCode:e})=>(0,n._)`{pattern: ${e}}`};const a={keyword:"pattern",type:"string",schemaType:"string",$data:true,error:o,code(e){const{data:t,$data:r,schema:o,schemaCode:a,it:i}=e;const c=i.opts.unicodeRegExp?"u":"";const u=r?(0,n._)`(new RegExp(${a}, ${c}))`:(0,s.usePattern)(e,o);e.fail$data((0,n._)`!${u}.test(${t})`)}};t["default"]=a},34003:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(15765);const n=r(99029);const o=r(94227);const a={message:({params:{missingProperty:e}})=>(0,n.str)`must have required property '${e}'`,params:({params:{missingProperty:e}})=>(0,n._)`{missingProperty: ${e}}`};const i={keyword:"required",type:"object",schemaType:"array",$data:true,error:a,code(e){const{gen:t,schema:r,schemaCode:a,data:i,$data:c,it:u}=e;const{opts:d}=u;if(!c&&r.length===0)return;const l=r.length>=d.loopRequired;if(u.allErrors)f();else h();if(d.strictRequired){const t=e.parentSchema.properties;const{definedProperties:s}=e.it;for(const e of r){if((t===null||t===void 0?void 0:t[e])===undefined&&!s.has(e)){const t=u.schemaEnv.baseId+u.errSchemaPath;const r=`required property "${e}" is not defined at "${t}" (strictRequired)`;(0,o.checkStrictMode)(u,r,u.opts.strictRequired)}}}function f(){if(l||c){e.block$data(n.nil,p)}else{for(const t of r){(0,s.checkReportMissingProp)(e,t)}}}function h(){const n=t.let("missing");if(l||c){const r=t.let("valid",true);e.block$data(r,(()=>m(n,r)));e.ok(r)}else{t.if((0,s.checkMissingProp)(e,r,n));(0,s.reportMissingProp)(e,n);t.else()}}function p(){t.forOf("prop",a,(r=>{e.setParams({missingProperty:r});t.if((0,s.noPropertyInData)(t,i,r,d.ownProperties),(()=>e.error()))}))}function m(r,o){e.setParams({missingProperty:r});t.forOf(r,a,(()=>{t.assign(o,(0,s.propertyInData)(t,i,r,d.ownProperties));t.if((0,n.not)(o),(()=>{e.error();t.break()}))}),n.nil)}}};t["default"]=i},60617:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:true});const s=r(10208);const n=r(99029);const o=r(94227);const a=r(76250);const i={message:({params:{i:e,j:t}})=>(0,n.str)`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>(0,n._)`{i: ${e}, j: ${t}}`};const c={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:true,error:i,code(e){const{gen:t,data:r,$data:i,schema:c,parentSchema:u,schemaCode:d,it:l}=e;if(!i&&!c)return;const f=t.let("valid");const h=u.items?(0,s.getSchemaTypes)(u.items):[];e.block$data(f,p,(0,n._)`${d} === false`);e.ok(f);function p(){const s=t.let("i",(0,n._)`${r}.length`);const o=t.let("j");e.setParams({i:s,j:o});t.assign(f,true);t.if((0,n._)`${s} > 1`,(()=>(m()?y:g)(s,o)))}function m(){return h.length>0&&!h.some((e=>e==="object"||e==="array"))}function y(o,a){const i=t.name("item");const c=(0,s.checkDataTypes)(h,i,l.opts.strictNumbers,s.DataType.Wrong);const u=t.const("indices",(0,n._)`{}`);t.for((0,n._)`;${o}--;`,(()=>{t.let(i,(0,n._)`${r}[${o}]`);t.if(c,(0,n._)`continue`);if(h.length>1)t.if((0,n._)`typeof ${i} == "string"`,(0,n._)`${i} += "_"`);t.if((0,n._)`typeof ${u}[${i}] == "number"`,(()=>{t.assign(a,(0,n._)`${u}[${i}]`);e.error();t.assign(f,false).break()})).code((0,n._)`${u}[${i}] = ${o}`)}))}function g(s,i){const c=(0,o.useFunc)(t,a.default);const u=t.name("outer");t.label(u).for((0,n._)`;${s}--;`,(()=>t.for((0,n._)`${i} = ${s}; ${i}--;`,(()=>t.if((0,n._)`${c}(${r}[${s}], ${r}[${i}])`,(()=>{e.error();t.assign(f,false).break(u)}))))))}}};t["default"]=c},32017:e=>{e.exports=function e(t,r){if(t===r)return true;if(t&&r&&typeof t=="object"&&typeof r=="object"){if(t.constructor!==r.constructor)return false;var s,n,o;if(Array.isArray(t)){s=t.length;if(s!=r.length)return false;for(n=s;n--!==0;)if(!e(t[n],r[n]))return false;return true}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();o=Object.keys(t);s=o.length;if(s!==Object.keys(r).length)return false;for(n=s;n--!==0;)if(!Object.prototype.hasOwnProperty.call(r,o[n]))return false;for(n=s;n--!==0;){var a=o[n];if(!e(t[a],r[a]))return false}return true}return t!==t&&r!==r}},74488:(e,t,r)=>{const{normalizeIPv6:s,normalizeIPv4:n,removeDotSegments:o,recomposeAuthority:a,normalizeComponentEncoding:i}=r(9245);const c=r(49884);function u(e,t){if(typeof e==="string"){e=h(g(e,t),t)}else if(typeof e==="object"){e=g(h(e,t),t)}return e}function d(e,t,r){const s=Object.assign({scheme:"null"},r);const n=l(g(e,s),g(t,s),s,true);return h(n,{...s,skipEscape:true})}function l(e,t,r,s){const n={};if(!s){e=g(h(e,r),r);t=g(h(t,r),r)}r=r||{};if(!r.tolerant&&t.scheme){n.scheme=t.scheme;n.userinfo=t.userinfo;n.host=t.host;n.port=t.port;n.path=o(t.path||"");n.query=t.query}else{if(t.userinfo!==undefined||t.host!==undefined||t.port!==undefined){n.userinfo=t.userinfo;n.host=t.host;n.port=t.port;n.path=o(t.path||"");n.query=t.query}else{if(!t.path){n.path=e.path;if(t.query!==undefined){n.query=t.query}else{n.query=e.query}}else{if(t.path.charAt(0)==="/"){n.path=o(t.path)}else{if((e.userinfo!==undefined||e.host!==undefined||e.port!==undefined)&&!e.path){n.path="/"+t.path}else if(!e.path){n.path=t.path}else{n.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path}n.path=o(n.path)}n.query=t.query}n.userinfo=e.userinfo;n.host=e.host;n.port=e.port}n.scheme=e.scheme}n.fragment=t.fragment;return n}function f(e,t,r){if(typeof e==="string"){e=unescape(e);e=h(i(g(e,r),true),{...r,skipEscape:true})}else if(typeof e==="object"){e=h(i(e,true),{...r,skipEscape:true})}if(typeof t==="string"){t=unescape(t);t=h(i(g(t,r),true),{...r,skipEscape:true})}else if(typeof t==="object"){t=h(i(t,true),{...r,skipEscape:true})}return e.toLowerCase()===t.toLowerCase()}function h(e,t){const r={host:e.host,scheme:e.scheme,userinfo:e.userinfo,port:e.port,path:e.path,query:e.query,nid:e.nid,nss:e.nss,uuid:e.uuid,fragment:e.fragment,reference:e.reference,resourceName:e.resourceName,secure:e.secure,error:""};const s=Object.assign({},t);const n=[];const i=c[(s.scheme||r.scheme||"").toLowerCase()];if(i&&i.serialize)i.serialize(r,s);if(r.path!==undefined){if(!s.skipEscape){r.path=escape(r.path);if(r.scheme!==undefined){r.path=r.path.split("%3A").join(":")}}else{r.path=unescape(r.path)}}if(s.reference!=="suffix"&&r.scheme){n.push(r.scheme,":")}const u=a(r);if(u!==undefined){if(s.reference!=="suffix"){n.push("//")}n.push(u);if(r.path&&r.path.charAt(0)!=="/"){n.push("/")}}if(r.path!==undefined){let e=r.path;if(!s.absolutePath&&(!i||!i.absolutePath)){e=o(e)}if(u===undefined){e=e.replace(/^\/\//u,"/%2F")}n.push(e)}if(r.query!==undefined){n.push("?",r.query)}if(r.fragment!==undefined){n.push("#",r.fragment)}return n.join("")}const p=Array.from({length:127},((e,t)=>/[^!"$&'()*+,\-.;=_`a-z{}~]/u.test(String.fromCharCode(t))));function m(e){let t=0;for(let r=0,s=e.length;r<s;++r){t=e.charCodeAt(r);if(t>126||p[t]){return true}}return false}const y=/^(?:([^#/:?]+):)?(?:\/\/((?:([^#/?@]*)@)?(\[[^#/?\]]+\]|[^#/:?]*)(?::(\d*))?))?([^#?]*)(?:\?([^#]*))?(?:#((?:.|[\n\r])*))?/u;function g(e,t){const r=Object.assign({},t);const o={scheme:undefined,userinfo:undefined,host:"",port:undefined,path:"",query:undefined,fragment:undefined};const a=e.indexOf("%")!==-1;let i=false;if(r.reference==="suffix")e=(r.scheme?r.scheme+":":"")+"//"+e;const u=e.match(y);if(u){o.scheme=u[1];o.userinfo=u[3];o.host=u[4];o.port=parseInt(u[5],10);o.path=u[6]||"";o.query=u[7];o.fragment=u[8];if(isNaN(o.port)){o.port=u[5]}if(o.host){const e=n(o.host);if(e.isIPV4===false){const t=s(e.host);o.host=t.host.toLowerCase();i=t.isIPV6}else{o.host=e.host;i=true}}if(o.scheme===undefined&&o.userinfo===undefined&&o.host===undefined&&o.port===undefined&&o.query===undefined&&!o.path){o.reference="same-document"}else if(o.scheme===undefined){o.reference="relative"}else if(o.fragment===undefined){o.reference="absolute"}else{o.reference="uri"}if(r.reference&&r.reference!=="suffix"&&r.reference!==o.reference){o.error=o.error||"URI is not a "+r.reference+" reference."}const e=c[(r.scheme||o.scheme||"").toLowerCase()];if(!r.unicodeSupport&&(!e||!e.unicodeSupport)){if(o.host&&(r.domainHost||e&&e.domainHost)&&i===false&&m(o.host)){try{o.host=URL.domainToASCII(o.host.toLowerCase())}catch(d){o.error=o.error||"Host's domain name can not be converted to ASCII: "+d}}}if(!e||e&&!e.skipNormalize){if(a&&o.scheme!==undefined){o.scheme=unescape(o.scheme)}if(a&&o.host!==undefined){o.host=unescape(o.host)}if(o.path){o.path=escape(unescape(o.path))}if(o.fragment){o.fragment=encodeURI(decodeURIComponent(o.fragment))}}if(e&&e.parse){e.parse(o,r)}}else{o.error=o.error||"URI can not be parsed."}return o}const $={SCHEMES:c,normalize:u,resolve:d,resolveComponents:l,equal:f,serialize:h,parse:g};e.exports=$;e.exports["default"]=$;e.exports.fastUri=$},49884:e=>{const t=/^[\da-f]{8}-[\da-f]{4}-[\da-f]{4}-[\da-f]{4}-[\da-f]{12}$/iu;const r=/([\da-z][\d\-a-z]{0,31}):((?:[\w!$'()*+,\-.:;=@]|%[\da-f]{2})+)/iu;function s(e){return typeof e.secure==="boolean"?e.secure:String(e.scheme).toLowerCase()==="wss"}function n(e){if(!e.host){e.error=e.error||"HTTP URIs must have a host."}return e}function o(e){const t=String(e.scheme).toLowerCase()==="https";if(e.port===(t?443:80)||e.port===""){e.port=undefined}if(!e.path){e.path="/"}return e}function a(e){e.secure=s(e);e.resourceName=(e.path||"/")+(e.query?"?"+e.query:"");e.path=undefined;e.query=undefined;return e}function i(e){if(e.port===(s(e)?443:80)||e.port===""){e.port=undefined}if(typeof e.secure==="boolean"){e.scheme=e.secure?"wss":"ws";e.secure=undefined}if(e.resourceName){const[t,r]=e.resourceName.split("?");e.path=t&&t!=="/"?t:undefined;e.query=r;e.resourceName=undefined}e.fragment=undefined;return e}function c(e,t){if(!e.path){e.error="URN can not be parsed";return e}const s=e.path.match(r);if(s){const r=t.scheme||e.scheme||"urn";e.nid=s[1].toLowerCase();e.nss=s[2];const n=`${r}:${t.nid||e.nid}`;const o=$[n];e.path=undefined;if(o){e=o.parse(e,t)}}else{e.error=e.error||"URN can not be parsed."}return e}function u(e,t){const r=t.scheme||e.scheme||"urn";const s=e.nid.toLowerCase();const n=`${r}:${t.nid||s}`;const o=$[n];if(o){e=o.serialize(e,t)}const a=e;const i=e.nss;a.path=`${s||t.nid}:${i}`;t.skipEscape=true;return a}function d(e,r){const s=e;s.uuid=s.nss;s.nss=undefined;if(!r.tolerant&&(!s.uuid||!t.test(s.uuid))){s.error=s.error||"UUID is not valid."}return s}function l(e){const t=e;t.nss=(e.uuid||"").toLowerCase();return t}const f={scheme:"http",domainHost:true,parse:n,serialize:o};const h={scheme:"https",domainHost:f.domainHost,parse:n,serialize:o};const p={scheme:"ws",domainHost:true,parse:a,serialize:i};const m={scheme:"wss",domainHost:p.domainHost,parse:p.parse,serialize:p.serialize};const y={scheme:"urn",parse:c,serialize:u,skipNormalize:true};const g={scheme:"urn:uuid",parse:d,serialize:l,skipNormalize:true};const $={http:f,https:h,ws:p,wss:m,urn:y,"urn:uuid":g};e.exports=$},54249:e=>{const t={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};e.exports={HEX:t}},9245:(e,t,r)=>{const{HEX:s}=r(54249);const n=/^(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)$/u;function o(e){if(d(e,".")<3){return{host:e,isIPV4:false}}const t=e.match(n)||[];const[r]=t;if(r){return{host:u(r,"."),isIPV4:true}}else{return{host:e,isIPV4:false}}}function a(e,t=false){let r="";let n=true;for(const o of e){if(s[o]===undefined)return undefined;if(o!=="0"&&n===true)n=false;if(!n)r+=o}if(t&&r.length===0)r="0";return r}function i(e){let t=0;const r={error:false,address:"",zone:""};const s=[];const n=[];let o=false;let i=false;let c=false;function u(){if(n.length){if(o===false){const e=a(n);if(e!==undefined){s.push(e)}else{r.error=true;return false}}n.length=0}return true}for(let a=0;a<e.length;a++){const d=e[a];if(d==="["||d==="]"){continue}if(d===":"){if(i===true){c=true}if(!u()){break}t++;s.push(":");if(t>7){r.error=true;break}if(a-1>=0&&e[a-1]===":"){i=true}continue}else if(d==="%"){if(!u()){break}o=true}else{n.push(d);continue}}if(n.length){if(o){r.zone=n.join("")}else if(c){s.push(n.join(""))}else{s.push(a(n))}}r.address=s.join("");return r}function c(e){if(d(e,":")<2){return{host:e,isIPV6:false}}const t=i(e);if(!t.error){let e=t.address;let r=t.address;if(t.zone){e+="%"+t.zone;r+="%25"+t.zone}return{host:e,escapedHost:r,isIPV6:true}}else{return{host:e,isIPV6:false}}}function u(e,t){let r="";let s=true;const n=e.length;for(let o=0;o<n;o++){const a=e[o];if(a==="0"&&s){if(o+1<=n&&e[o+1]===t||o+1===n){r+=a;s=false}}else{if(a===t){s=true}else{s=false}r+=a}}return r}function d(e,t){let r=0;for(let s=0;s<e.length;s++){if(e[s]===t)r++}return r}const l=/^\.\.?\//u;const f=/^\/\.(?:\/|$)/u;const h=/^\/\.\.(?:\/|$)/u;const p=/^\/?(?:.|\n)*?(?=\/|$)/u;function m(e){const t=[];while(e.length){if(e.match(l)){e=e.replace(l,"")}else if(e.match(f)){e=e.replace(f,"/")}else if(e.match(h)){e=e.replace(h,"/");t.pop()}else if(e==="."||e===".."){e=""}else{const r=e.match(p);if(r){const s=r[0];e=e.slice(s.length);t.push(s)}else{throw new Error("Unexpected dot segment condition")}}}return t.join("")}function y(e,t){const r=t!==true?escape:unescape;if(e.scheme!==undefined){e.scheme=r(e.scheme)}if(e.userinfo!==undefined){e.userinfo=r(e.userinfo)}if(e.host!==undefined){e.host=r(e.host)}if(e.path!==undefined){e.path=r(e.path)}if(e.query!==undefined){e.query=r(e.query)}if(e.fragment!==undefined){e.fragment=r(e.fragment)}return e}function g(e){const t=[];if(e.userinfo!==undefined){t.push(e.userinfo);t.push("@")}if(e.host!==undefined){let r=unescape(e.host);const s=o(r);if(s.isIPV4){r=s.host}else{const t=c(s.host);if(t.isIPV6===true){r=`[${t.escapedHost}]`}else{r=e.host}}t.push(r)}if(typeof e.port==="number"||typeof e.port==="string"){t.push(":");t.push(String(e.port))}return t.length?t.join(""):undefined}e.exports={recomposeAuthority:g,normalizeComponentEncoding:y,removeDotSegments:m,normalizeIPv4:o,normalizeIPv6:c,stringArrayToHexStripped:a}},7106:e=>{var t=e.exports=function(e,t,s){if(typeof t=="function"){s=t;t={}}s=t.cb||s;var n=typeof s=="function"?s:s.pre||function(){};var o=s.post||function(){};r(t,n,o,e,"",e)};t.keywords={additionalItems:true,items:true,contains:true,additionalProperties:true,propertyNames:true,not:true,if:true,then:true,else:true};t.arrayKeywords={items:true,allOf:true,anyOf:true,oneOf:true};t.propsKeywords={$defs:true,definitions:true,properties:true,patternProperties:true,dependencies:true};t.skipKeywords={default:true,enum:true,const:true,required:true,maximum:true,minimum:true,exclusiveMaximum:true,exclusiveMinimum:true,multipleOf:true,maxLength:true,minLength:true,pattern:true,format:true,maxItems:true,minItems:true,uniqueItems:true,maxProperties:true,minProperties:true};function r(e,n,o,a,i,c,u,d,l,f){if(a&&typeof a=="object"&&!Array.isArray(a)){n(a,i,c,u,d,l,f);for(var h in a){var p=a[h];if(Array.isArray(p)){if(h in t.arrayKeywords){for(var m=0;m<p.length;m++)r(e,n,o,p[m],i+"/"+h+"/"+m,c,i,h,a,m)}}else if(h in t.propsKeywords){if(p&&typeof p=="object"){for(var y in p)r(e,n,o,p[y],i+"/"+h+"/"+s(y),c,i,h,a,y)}}else if(h in t.keywords||e.allKeys&&!(h in t.skipKeywords)){r(e,n,o,p,i+"/"+h,c,i,h,a)}}o(a,i,c,u,d,l,f)}}function s(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}},63837:e=>{e.exports=JSON.parse('{"$id":"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#","description":"Meta-schema for $data reference (JSON AnySchema extension proposal)","type":"object","required":["$data"],"properties":{"$data":{"type":"string","anyOf":[{"format":"relative-json-pointer"},{"format":"json-pointer"}]}},"additionalProperties":false}')},72079:e=>{e.exports=JSON.parse('{"$schema":"http://json-schema.org/draft-07/schema#","$id":"http://json-schema.org/draft-07/schema#","title":"Core schema meta-schema","definitions":{"schemaArray":{"type":"array","minItems":1,"items":{"$ref":"#"}},"nonNegativeInteger":{"type":"integer","minimum":0},"nonNegativeIntegerDefault0":{"allOf":[{"$ref":"#/definitions/nonNegativeInteger"},{"default":0}]},"simpleTypes":{"enum":["array","boolean","integer","null","number","object","string"]},"stringArray":{"type":"array","items":{"type":"string"},"uniqueItems":true,"default":[]}},"type":["object","boolean"],"properties":{"$id":{"type":"string","format":"uri-reference"},"$schema":{"type":"string","format":"uri"},"$ref":{"type":"string","format":"uri-reference"},"$comment":{"type":"string"},"title":{"type":"string"},"description":{"type":"string"},"default":true,"readOnly":{"type":"boolean","default":false},"examples":{"type":"array","items":true},"multipleOf":{"type":"number","exclusiveMinimum":0},"maximum":{"type":"number"},"exclusiveMaximum":{"type":"number"},"minimum":{"type":"number"},"exclusiveMinimum":{"type":"number"},"maxLength":{"$ref":"#/definitions/nonNegativeInteger"},"minLength":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"pattern":{"type":"string","format":"regex"},"additionalItems":{"$ref":"#"},"items":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/schemaArray"}],"default":true},"maxItems":{"$ref":"#/definitions/nonNegativeInteger"},"minItems":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"uniqueItems":{"type":"boolean","default":false},"contains":{"$ref":"#"},"maxProperties":{"$ref":"#/definitions/nonNegativeInteger"},"minProperties":{"$ref":"#/definitions/nonNegativeIntegerDefault0"},"required":{"$ref":"#/definitions/stringArray"},"additionalProperties":{"$ref":"#"},"definitions":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"properties":{"type":"object","additionalProperties":{"$ref":"#"},"default":{}},"patternProperties":{"type":"object","additionalProperties":{"$ref":"#"},"propertyNames":{"format":"regex"},"default":{}},"dependencies":{"type":"object","additionalProperties":{"anyOf":[{"$ref":"#"},{"$ref":"#/definitions/stringArray"}]}},"propertyNames":{"$ref":"#"},"const":true,"enum":{"type":"array","items":true,"minItems":1,"uniqueItems":true},"type":{"anyOf":[{"$ref":"#/definitions/simpleTypes"},{"type":"array","items":{"$ref":"#/definitions/simpleTypes"},"minItems":1,"uniqueItems":true}]},"format":{"type":"string"},"contentMediaType":{"type":"string"},"contentEncoding":{"type":"string"},"if":{"$ref":"#"},"then":{"$ref":"#"},"else":{"$ref":"#"},"allOf":{"$ref":"#/definitions/schemaArray"},"anyOf":{"$ref":"#/definitions/schemaArray"},"oneOf":{"$ref":"#/definitions/schemaArray"},"not":{"$ref":"#"}},"default":true}')}}]);
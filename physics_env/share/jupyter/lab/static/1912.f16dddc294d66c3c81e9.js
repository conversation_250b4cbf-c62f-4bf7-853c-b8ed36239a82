"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[1912],{71912:(t,e,a)=>{a.d(e,{diagram:()=>Kt});var r=a(60148);var i=a(96049);var n=a(75905);var s=a(24982);var l=a(16750);var o=function(){var t=(0,n.K2)((function(t,e,a,r){for(a=a||{},r=t.length;r--;a[t[r]]=e);return a}),"o"),e=[1,24],a=[1,25],r=[1,26],i=[1,27],s=[1,28],l=[1,63],o=[1,64],h=[1,65],d=[1,66],u=[1,67],p=[1,68],f=[1,69],y=[1,29],b=[1,30],g=[1,31],x=[1,32],_=[1,33],m=[1,34],v=[1,35],k=[1,36],E=[1,37],S=[1,38],A=[1,39],C=[1,40],w=[1,41],O=[1,42],T=[1,43],R=[1,44],D=[1,45],N=[1,46],P=[1,47],B=[1,48],j=[1,50],I=[1,51],M=[1,52],K=[1,53],L=[1,54],Y=[1,55],U=[1,56],F=[1,57],X=[1,58],z=[1,59],W=[1,60],Q=[14,42],$=[14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],H=[12,14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],q=[1,82],V=[1,83],G=[1,84],J=[1,85],Z=[12,14,42],tt=[12,14,33,42],et=[12,14,33,42,76,77,79,80],at=[12,33],rt=[34,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74];var it={trace:(0,n.K2)((function t(){}),"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,direction:5,direction_tb:6,direction_bt:7,direction_rl:8,direction_lr:9,graphConfig:10,C4_CONTEXT:11,NEWLINE:12,statements:13,EOF:14,C4_CONTAINER:15,C4_COMPONENT:16,C4_DYNAMIC:17,C4_DEPLOYMENT:18,otherStatements:19,diagramStatements:20,otherStatement:21,title:22,accDescription:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,boundaryStatement:29,boundaryStartStatement:30,boundaryStopStatement:31,boundaryStart:32,LBRACE:33,ENTERPRISE_BOUNDARY:34,attributes:35,SYSTEM_BOUNDARY:36,BOUNDARY:37,CONTAINER_BOUNDARY:38,NODE:39,NODE_L:40,NODE_R:41,RBRACE:42,diagramStatement:43,PERSON:44,PERSON_EXT:45,SYSTEM:46,SYSTEM_DB:47,SYSTEM_QUEUE:48,SYSTEM_EXT:49,SYSTEM_EXT_DB:50,SYSTEM_EXT_QUEUE:51,CONTAINER:52,CONTAINER_DB:53,CONTAINER_QUEUE:54,CONTAINER_EXT:55,CONTAINER_EXT_DB:56,CONTAINER_EXT_QUEUE:57,COMPONENT:58,COMPONENT_DB:59,COMPONENT_QUEUE:60,COMPONENT_EXT:61,COMPONENT_EXT_DB:62,COMPONENT_EXT_QUEUE:63,REL:64,BIREL:65,REL_U:66,REL_D:67,REL_L:68,REL_R:69,REL_B:70,REL_INDEX:71,UPDATE_EL_STYLE:72,UPDATE_REL_STYLE:73,UPDATE_LAYOUT_CONFIG:74,attribute:75,STR:76,STR_KEY:77,STR_VALUE:78,ATTRIBUTE:79,ATTRIBUTE_EMPTY:80,$accept:0,$end:1},terminals_:{2:"error",6:"direction_tb",7:"direction_bt",8:"direction_rl",9:"direction_lr",11:"C4_CONTEXT",12:"NEWLINE",14:"EOF",15:"C4_CONTAINER",16:"C4_COMPONENT",17:"C4_DYNAMIC",18:"C4_DEPLOYMENT",22:"title",23:"accDescription",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"LBRACE",34:"ENTERPRISE_BOUNDARY",36:"SYSTEM_BOUNDARY",37:"BOUNDARY",38:"CONTAINER_BOUNDARY",39:"NODE",40:"NODE_L",41:"NODE_R",42:"RBRACE",44:"PERSON",45:"PERSON_EXT",46:"SYSTEM",47:"SYSTEM_DB",48:"SYSTEM_QUEUE",49:"SYSTEM_EXT",50:"SYSTEM_EXT_DB",51:"SYSTEM_EXT_QUEUE",52:"CONTAINER",53:"CONTAINER_DB",54:"CONTAINER_QUEUE",55:"CONTAINER_EXT",56:"CONTAINER_EXT_DB",57:"CONTAINER_EXT_QUEUE",58:"COMPONENT",59:"COMPONENT_DB",60:"COMPONENT_QUEUE",61:"COMPONENT_EXT",62:"COMPONENT_EXT_DB",63:"COMPONENT_EXT_QUEUE",64:"REL",65:"BIREL",66:"REL_U",67:"REL_D",68:"REL_L",69:"REL_R",70:"REL_B",71:"REL_INDEX",72:"UPDATE_EL_STYLE",73:"UPDATE_REL_STYLE",74:"UPDATE_LAYOUT_CONFIG",76:"STR",77:"STR_KEY",78:"STR_VALUE",79:"ATTRIBUTE",80:"ATTRIBUTE_EMPTY"},productions_:[0,[3,1],[3,1],[5,1],[5,1],[5,1],[5,1],[4,1],[10,4],[10,4],[10,4],[10,4],[10,4],[13,1],[13,1],[13,2],[19,1],[19,2],[19,3],[21,1],[21,1],[21,2],[21,2],[21,1],[29,3],[30,3],[30,3],[30,4],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[31,1],[20,1],[20,2],[20,3],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,1],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[35,1],[35,2],[75,1],[75,2],[75,1],[75,1]],performAction:(0,n.K2)((function t(e,a,r,i,n,s,l){var o=s.length-1;switch(n){case 3:i.setDirection("TB");break;case 4:i.setDirection("BT");break;case 5:i.setDirection("RL");break;case 6:i.setDirection("LR");break;case 8:case 9:case 10:case 11:case 12:i.setC4Type(s[o-3]);break;case 19:i.setTitle(s[o].substring(6));this.$=s[o].substring(6);break;case 20:i.setAccDescription(s[o].substring(15));this.$=s[o].substring(15);break;case 21:this.$=s[o].trim();i.setTitle(this.$);break;case 22:case 23:this.$=s[o].trim();i.setAccDescription(this.$);break;case 28:s[o].splice(2,0,"ENTERPRISE");i.addPersonOrSystemBoundary(...s[o]);this.$=s[o];break;case 29:s[o].splice(2,0,"SYSTEM");i.addPersonOrSystemBoundary(...s[o]);this.$=s[o];break;case 30:i.addPersonOrSystemBoundary(...s[o]);this.$=s[o];break;case 31:s[o].splice(2,0,"CONTAINER");i.addContainerBoundary(...s[o]);this.$=s[o];break;case 32:i.addDeploymentNode("node",...s[o]);this.$=s[o];break;case 33:i.addDeploymentNode("nodeL",...s[o]);this.$=s[o];break;case 34:i.addDeploymentNode("nodeR",...s[o]);this.$=s[o];break;case 35:i.popBoundaryParseStack();break;case 39:i.addPersonOrSystem("person",...s[o]);this.$=s[o];break;case 40:i.addPersonOrSystem("external_person",...s[o]);this.$=s[o];break;case 41:i.addPersonOrSystem("system",...s[o]);this.$=s[o];break;case 42:i.addPersonOrSystem("system_db",...s[o]);this.$=s[o];break;case 43:i.addPersonOrSystem("system_queue",...s[o]);this.$=s[o];break;case 44:i.addPersonOrSystem("external_system",...s[o]);this.$=s[o];break;case 45:i.addPersonOrSystem("external_system_db",...s[o]);this.$=s[o];break;case 46:i.addPersonOrSystem("external_system_queue",...s[o]);this.$=s[o];break;case 47:i.addContainer("container",...s[o]);this.$=s[o];break;case 48:i.addContainer("container_db",...s[o]);this.$=s[o];break;case 49:i.addContainer("container_queue",...s[o]);this.$=s[o];break;case 50:i.addContainer("external_container",...s[o]);this.$=s[o];break;case 51:i.addContainer("external_container_db",...s[o]);this.$=s[o];break;case 52:i.addContainer("external_container_queue",...s[o]);this.$=s[o];break;case 53:i.addComponent("component",...s[o]);this.$=s[o];break;case 54:i.addComponent("component_db",...s[o]);this.$=s[o];break;case 55:i.addComponent("component_queue",...s[o]);this.$=s[o];break;case 56:i.addComponent("external_component",...s[o]);this.$=s[o];break;case 57:i.addComponent("external_component_db",...s[o]);this.$=s[o];break;case 58:i.addComponent("external_component_queue",...s[o]);this.$=s[o];break;case 60:i.addRel("rel",...s[o]);this.$=s[o];break;case 61:i.addRel("birel",...s[o]);this.$=s[o];break;case 62:i.addRel("rel_u",...s[o]);this.$=s[o];break;case 63:i.addRel("rel_d",...s[o]);this.$=s[o];break;case 64:i.addRel("rel_l",...s[o]);this.$=s[o];break;case 65:i.addRel("rel_r",...s[o]);this.$=s[o];break;case 66:i.addRel("rel_b",...s[o]);this.$=s[o];break;case 67:s[o].splice(0,1);i.addRel("rel",...s[o]);this.$=s[o];break;case 68:i.updateElStyle("update_el_style",...s[o]);this.$=s[o];break;case 69:i.updateRelStyle("update_rel_style",...s[o]);this.$=s[o];break;case 70:i.updateLayoutConfig("update_layout_config",...s[o]);this.$=s[o];break;case 71:this.$=[s[o]];break;case 72:s[o].unshift(s[o-1]);this.$=s[o];break;case 73:case 75:this.$=s[o].trim();break;case 74:let t={};t[s[o-1].trim()]=s[o].trim();this.$=t;break;case 76:this.$="";break}}),"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],7:[1,6],8:[1,7],9:[1,8],10:4,11:[1,9],15:[1,10],16:[1,11],17:[1,12],18:[1,13]},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,7]},{1:[2,3]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{12:[1,14]},{12:[1,15]},{12:[1,16]},{12:[1,17]},{12:[1,18]},{13:19,19:20,20:21,21:22,22:e,23:a,24:r,26:i,28:s,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:f,43:23,44:y,45:b,46:g,47:x,48:_,49:m,50:v,51:k,52:E,53:S,54:A,55:C,56:w,57:O,58:T,59:R,60:D,61:N,62:P,63:B,64:j,65:I,66:M,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{13:70,19:20,20:21,21:22,22:e,23:a,24:r,26:i,28:s,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:f,43:23,44:y,45:b,46:g,47:x,48:_,49:m,50:v,51:k,52:E,53:S,54:A,55:C,56:w,57:O,58:T,59:R,60:D,61:N,62:P,63:B,64:j,65:I,66:M,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{13:71,19:20,20:21,21:22,22:e,23:a,24:r,26:i,28:s,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:f,43:23,44:y,45:b,46:g,47:x,48:_,49:m,50:v,51:k,52:E,53:S,54:A,55:C,56:w,57:O,58:T,59:R,60:D,61:N,62:P,63:B,64:j,65:I,66:M,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{13:72,19:20,20:21,21:22,22:e,23:a,24:r,26:i,28:s,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:f,43:23,44:y,45:b,46:g,47:x,48:_,49:m,50:v,51:k,52:E,53:S,54:A,55:C,56:w,57:O,58:T,59:R,60:D,61:N,62:P,63:B,64:j,65:I,66:M,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{13:73,19:20,20:21,21:22,22:e,23:a,24:r,26:i,28:s,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:f,43:23,44:y,45:b,46:g,47:x,48:_,49:m,50:v,51:k,52:E,53:S,54:A,55:C,56:w,57:O,58:T,59:R,60:D,61:N,62:P,63:B,64:j,65:I,66:M,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{14:[1,74]},t(Q,[2,13],{43:23,29:49,30:61,32:62,20:75,34:l,36:o,37:h,38:d,39:u,40:p,41:f,44:y,45:b,46:g,47:x,48:_,49:m,50:v,51:k,52:E,53:S,54:A,55:C,56:w,57:O,58:T,59:R,60:D,61:N,62:P,63:B,64:j,65:I,66:M,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W}),t(Q,[2,14]),t($,[2,16],{12:[1,76]}),t(Q,[2,36],{12:[1,77]}),t(H,[2,19]),t(H,[2,20]),{25:[1,78]},{27:[1,79]},t(H,[2,23]),{35:80,75:81,76:q,77:V,79:G,80:J},{35:86,75:81,76:q,77:V,79:G,80:J},{35:87,75:81,76:q,77:V,79:G,80:J},{35:88,75:81,76:q,77:V,79:G,80:J},{35:89,75:81,76:q,77:V,79:G,80:J},{35:90,75:81,76:q,77:V,79:G,80:J},{35:91,75:81,76:q,77:V,79:G,80:J},{35:92,75:81,76:q,77:V,79:G,80:J},{35:93,75:81,76:q,77:V,79:G,80:J},{35:94,75:81,76:q,77:V,79:G,80:J},{35:95,75:81,76:q,77:V,79:G,80:J},{35:96,75:81,76:q,77:V,79:G,80:J},{35:97,75:81,76:q,77:V,79:G,80:J},{35:98,75:81,76:q,77:V,79:G,80:J},{35:99,75:81,76:q,77:V,79:G,80:J},{35:100,75:81,76:q,77:V,79:G,80:J},{35:101,75:81,76:q,77:V,79:G,80:J},{35:102,75:81,76:q,77:V,79:G,80:J},{35:103,75:81,76:q,77:V,79:G,80:J},{35:104,75:81,76:q,77:V,79:G,80:J},t(Z,[2,59]),{35:105,75:81,76:q,77:V,79:G,80:J},{35:106,75:81,76:q,77:V,79:G,80:J},{35:107,75:81,76:q,77:V,79:G,80:J},{35:108,75:81,76:q,77:V,79:G,80:J},{35:109,75:81,76:q,77:V,79:G,80:J},{35:110,75:81,76:q,77:V,79:G,80:J},{35:111,75:81,76:q,77:V,79:G,80:J},{35:112,75:81,76:q,77:V,79:G,80:J},{35:113,75:81,76:q,77:V,79:G,80:J},{35:114,75:81,76:q,77:V,79:G,80:J},{35:115,75:81,76:q,77:V,79:G,80:J},{20:116,29:49,30:61,32:62,34:l,36:o,37:h,38:d,39:u,40:p,41:f,43:23,44:y,45:b,46:g,47:x,48:_,49:m,50:v,51:k,52:E,53:S,54:A,55:C,56:w,57:O,58:T,59:R,60:D,61:N,62:P,63:B,64:j,65:I,66:M,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W},{12:[1,118],33:[1,117]},{35:119,75:81,76:q,77:V,79:G,80:J},{35:120,75:81,76:q,77:V,79:G,80:J},{35:121,75:81,76:q,77:V,79:G,80:J},{35:122,75:81,76:q,77:V,79:G,80:J},{35:123,75:81,76:q,77:V,79:G,80:J},{35:124,75:81,76:q,77:V,79:G,80:J},{35:125,75:81,76:q,77:V,79:G,80:J},{14:[1,126]},{14:[1,127]},{14:[1,128]},{14:[1,129]},{1:[2,8]},t(Q,[2,15]),t($,[2,17],{21:22,19:130,22:e,23:a,24:r,26:i,28:s}),t(Q,[2,37],{19:20,20:21,21:22,43:23,29:49,30:61,32:62,13:131,22:e,23:a,24:r,26:i,28:s,34:l,36:o,37:h,38:d,39:u,40:p,41:f,44:y,45:b,46:g,47:x,48:_,49:m,50:v,51:k,52:E,53:S,54:A,55:C,56:w,57:O,58:T,59:R,60:D,61:N,62:P,63:B,64:j,65:I,66:M,67:K,68:L,69:Y,70:U,71:F,72:X,73:z,74:W}),t(H,[2,21]),t(H,[2,22]),t(Z,[2,39]),t(tt,[2,71],{75:81,35:132,76:q,77:V,79:G,80:J}),t(et,[2,73]),{78:[1,133]},t(et,[2,75]),t(et,[2,76]),t(Z,[2,40]),t(Z,[2,41]),t(Z,[2,42]),t(Z,[2,43]),t(Z,[2,44]),t(Z,[2,45]),t(Z,[2,46]),t(Z,[2,47]),t(Z,[2,48]),t(Z,[2,49]),t(Z,[2,50]),t(Z,[2,51]),t(Z,[2,52]),t(Z,[2,53]),t(Z,[2,54]),t(Z,[2,55]),t(Z,[2,56]),t(Z,[2,57]),t(Z,[2,58]),t(Z,[2,60]),t(Z,[2,61]),t(Z,[2,62]),t(Z,[2,63]),t(Z,[2,64]),t(Z,[2,65]),t(Z,[2,66]),t(Z,[2,67]),t(Z,[2,68]),t(Z,[2,69]),t(Z,[2,70]),{31:134,42:[1,135]},{12:[1,136]},{33:[1,137]},t(at,[2,28]),t(at,[2,29]),t(at,[2,30]),t(at,[2,31]),t(at,[2,32]),t(at,[2,33]),t(at,[2,34]),{1:[2,9]},{1:[2,10]},{1:[2,11]},{1:[2,12]},t($,[2,18]),t(Q,[2,38]),t(tt,[2,72]),t(et,[2,74]),t(Z,[2,24]),t(Z,[2,35]),t(rt,[2,25]),t(rt,[2,26],{12:[1,138]}),t(rt,[2,27])],defaultActions:{2:[2,1],3:[2,2],4:[2,7],5:[2,3],6:[2,4],7:[2,5],8:[2,6],74:[2,8],126:[2,9],127:[2,10],128:[2,11],129:[2,12]},parseError:(0,n.K2)((function t(e,a){if(a.recoverable){this.trace(e)}else{var r=new Error(e);r.hash=a;throw r}}),"parseError"),parse:(0,n.K2)((function t(e){var a=this,r=[0],i=[],s=[null],l=[],o=this.table,c="",h=0,d=0,u=0,p=2,f=1;var y=l.slice.call(arguments,1);var b=Object.create(this.lexer);var g={yy:{}};for(var x in this.yy){if(Object.prototype.hasOwnProperty.call(this.yy,x)){g.yy[x]=this.yy[x]}}b.setInput(e,g.yy);g.yy.lexer=b;g.yy.parser=this;if(typeof b.yylloc=="undefined"){b.yylloc={}}var _=b.yylloc;l.push(_);var m=b.options&&b.options.ranges;if(typeof g.yy.parseError==="function"){this.parseError=g.yy.parseError}else{this.parseError=Object.getPrototypeOf(this).parseError}function v(t){r.length=r.length-2*t;s.length=s.length-t;l.length=l.length-t}(0,n.K2)(v,"popStack");function k(){var t;t=i.pop()||b.lex()||f;if(typeof t!=="number"){if(t instanceof Array){i=t;t=i.pop()}t=a.symbols_[t]||t}return t}(0,n.K2)(k,"lex");var E,S,A,C,w,O,T={},R,D,N,P;while(true){A=r[r.length-1];if(this.defaultActions[A]){C=this.defaultActions[A]}else{if(E===null||typeof E=="undefined"){E=k()}C=o[A]&&o[A][E]}if(typeof C==="undefined"||!C.length||!C[0]){var B="";P=[];for(R in o[A]){if(this.terminals_[R]&&R>p){P.push("'"+this.terminals_[R]+"'")}}if(b.showPosition){B="Parse error on line "+(h+1)+":\n"+b.showPosition()+"\nExpecting "+P.join(", ")+", got '"+(this.terminals_[E]||E)+"'"}else{B="Parse error on line "+(h+1)+": Unexpected "+(E==f?"end of input":"'"+(this.terminals_[E]||E)+"'")}this.parseError(B,{text:b.match,token:this.terminals_[E]||E,line:b.yylineno,loc:_,expected:P})}if(C[0]instanceof Array&&C.length>1){throw new Error("Parse Error: multiple actions possible at state: "+A+", token: "+E)}switch(C[0]){case 1:r.push(E);s.push(b.yytext);l.push(b.yylloc);r.push(C[1]);E=null;if(!S){d=b.yyleng;c=b.yytext;h=b.yylineno;_=b.yylloc;if(u>0){u--}}else{E=S;S=null}break;case 2:D=this.productions_[C[1]][1];T.$=s[s.length-D];T._$={first_line:l[l.length-(D||1)].first_line,last_line:l[l.length-1].last_line,first_column:l[l.length-(D||1)].first_column,last_column:l[l.length-1].last_column};if(m){T._$.range=[l[l.length-(D||1)].range[0],l[l.length-1].range[1]]}O=this.performAction.apply(T,[c,d,h,g.yy,C[1],s,l].concat(y));if(typeof O!=="undefined"){return O}if(D){r=r.slice(0,-1*D*2);s=s.slice(0,-1*D);l=l.slice(0,-1*D)}r.push(this.productions_[C[1]][0]);s.push(T.$);l.push(T._$);N=o[r[r.length-2]][r[r.length-1]];r.push(N);break;case 3:return true}}return true}),"parse")};var nt=function(){var t={EOF:1,parseError:(0,n.K2)((function t(e,a){if(this.yy.parser){this.yy.parser.parseError(e,a)}else{throw new Error(e)}}),"parseError"),setInput:(0,n.K2)((function(t,e){this.yy=e||this.yy||{};this._input=t;this._more=this._backtrack=this.done=false;this.yylineno=this.yyleng=0;this.yytext=this.matched=this.match="";this.conditionStack=["INITIAL"];this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0};if(this.options.ranges){this.yylloc.range=[0,0]}this.offset=0;return this}),"setInput"),input:(0,n.K2)((function(){var t=this._input[0];this.yytext+=t;this.yyleng++;this.offset++;this.match+=t;this.matched+=t;var e=t.match(/(?:\r\n?|\n).*/g);if(e){this.yylineno++;this.yylloc.last_line++}else{this.yylloc.last_column++}if(this.options.ranges){this.yylloc.range[1]++}this._input=this._input.slice(1);return t}),"input"),unput:(0,n.K2)((function(t){var e=t.length;var a=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input;this.yytext=this.yytext.substr(0,this.yytext.length-e);this.offset-=e;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1);this.matched=this.matched.substr(0,this.matched.length-1);if(a.length-1){this.yylineno-=a.length-1}var i=this.yylloc.range;this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===r.length?this.yylloc.first_column:0)+r[r.length-a.length].length-a[0].length:this.yylloc.first_column-e};if(this.options.ranges){this.yylloc.range=[i[0],i[0]+this.yyleng-e]}this.yyleng=this.yytext.length;return this}),"unput"),more:(0,n.K2)((function(){this._more=true;return this}),"more"),reject:(0,n.K2)((function(){if(this.options.backtrack_lexer){this._backtrack=true}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}return this}),"reject"),less:(0,n.K2)((function(t){this.unput(this.match.slice(t))}),"less"),pastInput:(0,n.K2)((function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")}),"pastInput"),upcomingInput:(0,n.K2)((function(){var t=this.match;if(t.length<20){t+=this._input.substr(0,20-t.length)}return(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")}),"upcomingInput"),showPosition:(0,n.K2)((function(){var t=this.pastInput();var e=new Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"}),"showPosition"),test_match:(0,n.K2)((function(t,e){var a,r,i;if(this.options.backtrack_lexer){i={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done};if(this.options.ranges){i.yylloc.range=this.yylloc.range.slice(0)}}r=t[0].match(/(?:\r\n?|\n).*/g);if(r){this.yylineno+=r.length}this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length};this.yytext+=t[0];this.match+=t[0];this.matches=t;this.yyleng=this.yytext.length;if(this.options.ranges){this.yylloc.range=[this.offset,this.offset+=this.yyleng]}this._more=false;this._backtrack=false;this._input=this._input.slice(t[0].length);this.matched+=t[0];a=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]);if(this.done&&this._input){this.done=false}if(a){return a}else if(this._backtrack){for(var n in i){this[n]=i[n]}return false}return false}),"test_match"),next:(0,n.K2)((function(){if(this.done){return this.EOF}if(!this._input){this.done=true}var t,e,a,r;if(!this._more){this.yytext="";this.match=""}var i=this._currentRules();for(var n=0;n<i.length;n++){a=this._input.match(this.rules[i[n]]);if(a&&(!e||a[0].length>e[0].length)){e=a;r=n;if(this.options.backtrack_lexer){t=this.test_match(a,i[n]);if(t!==false){return t}else if(this._backtrack){e=false;continue}else{return false}}else if(!this.options.flex){break}}}if(e){t=this.test_match(e,i[r]);if(t!==false){return t}return false}if(this._input===""){return this.EOF}else{return this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})}}),"next"),lex:(0,n.K2)((function t(){var e=this.next();if(e){return e}else{return this.lex()}}),"lex"),begin:(0,n.K2)((function t(e){this.conditionStack.push(e)}),"begin"),popState:(0,n.K2)((function t(){var e=this.conditionStack.length-1;if(e>0){return this.conditionStack.pop()}else{return this.conditionStack[0]}}),"popState"),_currentRules:(0,n.K2)((function t(){if(this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules}else{return this.conditions["INITIAL"].rules}}),"_currentRules"),topState:(0,n.K2)((function t(e){e=this.conditionStack.length-1-Math.abs(e||0);if(e>=0){return this.conditionStack[e]}else{return"INITIAL"}}),"topState"),pushState:(0,n.K2)((function t(e){this.begin(e)}),"pushState"),stateStackSize:(0,n.K2)((function t(){return this.conditionStack.length}),"stateStackSize"),options:{},performAction:(0,n.K2)((function t(e,a,r,i){var n=i;switch(r){case 0:return 6;break;case 1:return 7;break;case 2:return 8;break;case 3:return 9;break;case 4:return 22;break;case 5:return 23;break;case 6:this.begin("acc_title");return 24;break;case 7:this.popState();return"acc_title_value";break;case 8:this.begin("acc_descr");return 26;break;case 9:this.popState();return"acc_descr_value";break;case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";break;case 13:break;case 14:c;break;case 15:return 12;break;case 16:break;case 17:return 11;break;case 18:return 15;break;case 19:return 16;break;case 20:return 17;break;case 21:return 18;break;case 22:this.begin("person_ext");return 45;break;case 23:this.begin("person");return 44;break;case 24:this.begin("system_ext_queue");return 51;break;case 25:this.begin("system_ext_db");return 50;break;case 26:this.begin("system_ext");return 49;break;case 27:this.begin("system_queue");return 48;break;case 28:this.begin("system_db");return 47;break;case 29:this.begin("system");return 46;break;case 30:this.begin("boundary");return 37;break;case 31:this.begin("enterprise_boundary");return 34;break;case 32:this.begin("system_boundary");return 36;break;case 33:this.begin("container_ext_queue");return 57;break;case 34:this.begin("container_ext_db");return 56;break;case 35:this.begin("container_ext");return 55;break;case 36:this.begin("container_queue");return 54;break;case 37:this.begin("container_db");return 53;break;case 38:this.begin("container");return 52;break;case 39:this.begin("container_boundary");return 38;break;case 40:this.begin("component_ext_queue");return 63;break;case 41:this.begin("component_ext_db");return 62;break;case 42:this.begin("component_ext");return 61;break;case 43:this.begin("component_queue");return 60;break;case 44:this.begin("component_db");return 59;break;case 45:this.begin("component");return 58;break;case 46:this.begin("node");return 39;break;case 47:this.begin("node");return 39;break;case 48:this.begin("node_l");return 40;break;case 49:this.begin("node_r");return 41;break;case 50:this.begin("rel");return 64;break;case 51:this.begin("birel");return 65;break;case 52:this.begin("rel_u");return 66;break;case 53:this.begin("rel_u");return 66;break;case 54:this.begin("rel_d");return 67;break;case 55:this.begin("rel_d");return 67;break;case 56:this.begin("rel_l");return 68;break;case 57:this.begin("rel_l");return 68;break;case 58:this.begin("rel_r");return 69;break;case 59:this.begin("rel_r");return 69;break;case 60:this.begin("rel_b");return 70;break;case 61:this.begin("rel_index");return 71;break;case 62:this.begin("update_el_style");return 72;break;case 63:this.begin("update_rel_style");return 73;break;case 64:this.begin("update_layout_config");return 74;break;case 65:return"EOF_IN_STRUCT";break;case 66:this.begin("attribute");return"ATTRIBUTE_EMPTY";break;case 67:this.begin("attribute");break;case 68:this.popState();this.popState();break;case 69:return 80;break;case 70:break;case 71:return 80;break;case 72:this.begin("string");break;case 73:this.popState();break;case 74:return"STR";break;case 75:this.begin("string_kv");break;case 76:this.begin("string_kv_key");return"STR_KEY";break;case 77:this.popState();this.begin("string_kv_value");break;case 78:return"STR_VALUE";break;case 79:this.popState();this.popState();break;case 80:return"STR";break;case 81:return"LBRACE";break;case 82:return"RBRACE";break;case 83:return"SPACE";break;case 84:return"EOL";break;case 85:return 14;break}}),"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:title\s[^#\n;]+)/,/^(?:accDescription\s[^#\n;]+)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:C4Context\b)/,/^(?:C4Container\b)/,/^(?:C4Component\b)/,/^(?:C4Dynamic\b)/,/^(?:C4Deployment\b)/,/^(?:Person_Ext\b)/,/^(?:Person\b)/,/^(?:SystemQueue_Ext\b)/,/^(?:SystemDb_Ext\b)/,/^(?:System_Ext\b)/,/^(?:SystemQueue\b)/,/^(?:SystemDb\b)/,/^(?:System\b)/,/^(?:Boundary\b)/,/^(?:Enterprise_Boundary\b)/,/^(?:System_Boundary\b)/,/^(?:ContainerQueue_Ext\b)/,/^(?:ContainerDb_Ext\b)/,/^(?:Container_Ext\b)/,/^(?:ContainerQueue\b)/,/^(?:ContainerDb\b)/,/^(?:Container\b)/,/^(?:Container_Boundary\b)/,/^(?:ComponentQueue_Ext\b)/,/^(?:ComponentDb_Ext\b)/,/^(?:Component_Ext\b)/,/^(?:ComponentQueue\b)/,/^(?:ComponentDb\b)/,/^(?:Component\b)/,/^(?:Deployment_Node\b)/,/^(?:Node\b)/,/^(?:Node_L\b)/,/^(?:Node_R\b)/,/^(?:Rel\b)/,/^(?:BiRel\b)/,/^(?:Rel_Up\b)/,/^(?:Rel_U\b)/,/^(?:Rel_Down\b)/,/^(?:Rel_D\b)/,/^(?:Rel_Left\b)/,/^(?:Rel_L\b)/,/^(?:Rel_Right\b)/,/^(?:Rel_R\b)/,/^(?:Rel_Back\b)/,/^(?:RelIndex\b)/,/^(?:UpdateElementStyle\b)/,/^(?:UpdateRelStyle\b)/,/^(?:UpdateLayoutConfig\b)/,/^(?:$)/,/^(?:[(][ ]*[,])/,/^(?:[(])/,/^(?:[)])/,/^(?:,,)/,/^(?:,)/,/^(?:[ ]*["]["])/,/^(?:[ ]*["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:[ ]*[\$])/,/^(?:[^=]*)/,/^(?:[=][ ]*["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:[^,]+)/,/^(?:\{)/,/^(?:\})/,/^(?:[\s]+)/,/^(?:[\n\r]+)/,/^(?:$)/],conditions:{acc_descr_multiline:{rules:[11,12],inclusive:false},acc_descr:{rules:[9],inclusive:false},acc_title:{rules:[7],inclusive:false},string_kv_value:{rules:[78,79],inclusive:false},string_kv_key:{rules:[77],inclusive:false},string_kv:{rules:[76],inclusive:false},string:{rules:[73,74],inclusive:false},attribute:{rules:[68,69,70,71,72,75,80],inclusive:false},update_layout_config:{rules:[65,66,67,68],inclusive:false},update_rel_style:{rules:[65,66,67,68],inclusive:false},update_el_style:{rules:[65,66,67,68],inclusive:false},rel_b:{rules:[65,66,67,68],inclusive:false},rel_r:{rules:[65,66,67,68],inclusive:false},rel_l:{rules:[65,66,67,68],inclusive:false},rel_d:{rules:[65,66,67,68],inclusive:false},rel_u:{rules:[65,66,67,68],inclusive:false},rel_bi:{rules:[],inclusive:false},rel:{rules:[65,66,67,68],inclusive:false},node_r:{rules:[65,66,67,68],inclusive:false},node_l:{rules:[65,66,67,68],inclusive:false},node:{rules:[65,66,67,68],inclusive:false},index:{rules:[],inclusive:false},rel_index:{rules:[65,66,67,68],inclusive:false},component_ext_queue:{rules:[],inclusive:false},component_ext_db:{rules:[65,66,67,68],inclusive:false},component_ext:{rules:[65,66,67,68],inclusive:false},component_queue:{rules:[65,66,67,68],inclusive:false},component_db:{rules:[65,66,67,68],inclusive:false},component:{rules:[65,66,67,68],inclusive:false},container_boundary:{rules:[65,66,67,68],inclusive:false},container_ext_queue:{rules:[65,66,67,68],inclusive:false},container_ext_db:{rules:[65,66,67,68],inclusive:false},container_ext:{rules:[65,66,67,68],inclusive:false},container_queue:{rules:[65,66,67,68],inclusive:false},container_db:{rules:[65,66,67,68],inclusive:false},container:{rules:[65,66,67,68],inclusive:false},birel:{rules:[65,66,67,68],inclusive:false},system_boundary:{rules:[65,66,67,68],inclusive:false},enterprise_boundary:{rules:[65,66,67,68],inclusive:false},boundary:{rules:[65,66,67,68],inclusive:false},system_ext_queue:{rules:[65,66,67,68],inclusive:false},system_ext_db:{rules:[65,66,67,68],inclusive:false},system_ext:{rules:[65,66,67,68],inclusive:false},system_queue:{rules:[65,66,67,68],inclusive:false},system_db:{rules:[65,66,67,68],inclusive:false},system:{rules:[65,66,67,68],inclusive:false},person_ext:{rules:[65,66,67,68],inclusive:false},person:{rules:[65,66,67,68],inclusive:false},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,81,82,83,84,85],inclusive:true}}};return t}();it.lexer=nt;function st(){this.yy={}}(0,n.K2)(st,"Parser");st.prototype=it;it.Parser=st;return new st}();o.parser=o;var h=o;var d=[];var u=[""];var p="global";var f="";var y=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}];var b=[];var g="";var x=false;var _=4;var m=2;var v;var k=(0,n.K2)((function(){return v}),"getC4Type");var E=(0,n.K2)((function(t){let e=(0,n.jZ)(t,(0,n.D7)());v=e}),"setC4Type");var S=(0,n.K2)((function(t,e,a,r,i,n,s,l,o){if(t===void 0||t===null||e===void 0||e===null||a===void 0||a===null||r===void 0||r===null){return}let c={};const h=b.find((t=>t.from===e&&t.to===a));if(h){c=h}else{b.push(c)}c.type=t;c.from=e;c.to=a;c.label={text:r};if(i===void 0||i===null){c.techn={text:""}}else{if(typeof i==="object"){let[t,e]=Object.entries(i)[0];c[t]={text:e}}else{c.techn={text:i}}}if(n===void 0||n===null){c.descr={text:""}}else{if(typeof n==="object"){let[t,e]=Object.entries(n)[0];c[t]={text:e}}else{c.descr={text:n}}}if(typeof s==="object"){let[t,e]=Object.entries(s)[0];c[t]=e}else{c.sprite=s}if(typeof l==="object"){let[t,e]=Object.entries(l)[0];c[t]=e}else{c.tags=l}if(typeof o==="object"){let[t,e]=Object.entries(o)[0];c[t]=e}else{c.link=o}c.wrap=$()}),"addRel");var A=(0,n.K2)((function(t,e,a,r,i,n,s){if(e===null||a===null){return}let l={};const o=d.find((t=>t.alias===e));if(o&&e===o.alias){l=o}else{l.alias=e;d.push(l)}if(a===void 0||a===null){l.label={text:""}}else{l.label={text:a}}if(r===void 0||r===null){l.descr={text:""}}else{if(typeof r==="object"){let[t,e]=Object.entries(r)[0];l[t]={text:e}}else{l.descr={text:r}}}if(typeof i==="object"){let[t,e]=Object.entries(i)[0];l[t]=e}else{l.sprite=i}if(typeof n==="object"){let[t,e]=Object.entries(n)[0];l[t]=e}else{l.tags=n}if(typeof s==="object"){let[t,e]=Object.entries(s)[0];l[t]=e}else{l.link=s}l.typeC4Shape={text:t};l.parentBoundary=p;l.wrap=$()}),"addPersonOrSystem");var C=(0,n.K2)((function(t,e,a,r,i,n,s,l){if(e===null||a===null){return}let o={};const c=d.find((t=>t.alias===e));if(c&&e===c.alias){o=c}else{o.alias=e;d.push(o)}if(a===void 0||a===null){o.label={text:""}}else{o.label={text:a}}if(r===void 0||r===null){o.techn={text:""}}else{if(typeof r==="object"){let[t,e]=Object.entries(r)[0];o[t]={text:e}}else{o.techn={text:r}}}if(i===void 0||i===null){o.descr={text:""}}else{if(typeof i==="object"){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else{o.descr={text:i}}}if(typeof n==="object"){let[t,e]=Object.entries(n)[0];o[t]=e}else{o.sprite=n}if(typeof s==="object"){let[t,e]=Object.entries(s)[0];o[t]=e}else{o.tags=s}if(typeof l==="object"){let[t,e]=Object.entries(l)[0];o[t]=e}else{o.link=l}o.wrap=$();o.typeC4Shape={text:t};o.parentBoundary=p}),"addContainer");var w=(0,n.K2)((function(t,e,a,r,i,n,s,l){if(e===null||a===null){return}let o={};const c=d.find((t=>t.alias===e));if(c&&e===c.alias){o=c}else{o.alias=e;d.push(o)}if(a===void 0||a===null){o.label={text:""}}else{o.label={text:a}}if(r===void 0||r===null){o.techn={text:""}}else{if(typeof r==="object"){let[t,e]=Object.entries(r)[0];o[t]={text:e}}else{o.techn={text:r}}}if(i===void 0||i===null){o.descr={text:""}}else{if(typeof i==="object"){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else{o.descr={text:i}}}if(typeof n==="object"){let[t,e]=Object.entries(n)[0];o[t]=e}else{o.sprite=n}if(typeof s==="object"){let[t,e]=Object.entries(s)[0];o[t]=e}else{o.tags=s}if(typeof l==="object"){let[t,e]=Object.entries(l)[0];o[t]=e}else{o.link=l}o.wrap=$();o.typeC4Shape={text:t};o.parentBoundary=p}),"addComponent");var O=(0,n.K2)((function(t,e,a,r,i){if(t===null||e===null){return}let n={};const s=y.find((e=>e.alias===t));if(s&&t===s.alias){n=s}else{n.alias=t;y.push(n)}if(e===void 0||e===null){n.label={text:""}}else{n.label={text:e}}if(a===void 0||a===null){n.type={text:"system"}}else{if(typeof a==="object"){let[t,e]=Object.entries(a)[0];n[t]={text:e}}else{n.type={text:a}}}if(typeof r==="object"){let[t,e]=Object.entries(r)[0];n[t]=e}else{n.tags=r}if(typeof i==="object"){let[t,e]=Object.entries(i)[0];n[t]=e}else{n.link=i}n.parentBoundary=p;n.wrap=$();f=p;p=t;u.push(f)}),"addPersonOrSystemBoundary");var T=(0,n.K2)((function(t,e,a,r,i){if(t===null||e===null){return}let n={};const s=y.find((e=>e.alias===t));if(s&&t===s.alias){n=s}else{n.alias=t;y.push(n)}if(e===void 0||e===null){n.label={text:""}}else{n.label={text:e}}if(a===void 0||a===null){n.type={text:"container"}}else{if(typeof a==="object"){let[t,e]=Object.entries(a)[0];n[t]={text:e}}else{n.type={text:a}}}if(typeof r==="object"){let[t,e]=Object.entries(r)[0];n[t]=e}else{n.tags=r}if(typeof i==="object"){let[t,e]=Object.entries(i)[0];n[t]=e}else{n.link=i}n.parentBoundary=p;n.wrap=$();f=p;p=t;u.push(f)}),"addContainerBoundary");var R=(0,n.K2)((function(t,e,a,r,i,n,s,l){if(e===null||a===null){return}let o={};const c=y.find((t=>t.alias===e));if(c&&e===c.alias){o=c}else{o.alias=e;y.push(o)}if(a===void 0||a===null){o.label={text:""}}else{o.label={text:a}}if(r===void 0||r===null){o.type={text:"node"}}else{if(typeof r==="object"){let[t,e]=Object.entries(r)[0];o[t]={text:e}}else{o.type={text:r}}}if(i===void 0||i===null){o.descr={text:""}}else{if(typeof i==="object"){let[t,e]=Object.entries(i)[0];o[t]={text:e}}else{o.descr={text:i}}}if(typeof s==="object"){let[t,e]=Object.entries(s)[0];o[t]=e}else{o.tags=s}if(typeof l==="object"){let[t,e]=Object.entries(l)[0];o[t]=e}else{o.link=l}o.nodeType=t;o.parentBoundary=p;o.wrap=$();f=p;p=e;u.push(f)}),"addDeploymentNode");var D=(0,n.K2)((function(){p=f;u.pop();f=u.pop();u.push(f)}),"popBoundaryParseStack");var N=(0,n.K2)((function(t,e,a,r,i,n,s,l,o,c,h){let u=d.find((t=>t.alias===e));if(u===void 0){u=y.find((t=>t.alias===e));if(u===void 0){return}}if(a!==void 0&&a!==null){if(typeof a==="object"){let[t,e]=Object.entries(a)[0];u[t]=e}else{u.bgColor=a}}if(r!==void 0&&r!==null){if(typeof r==="object"){let[t,e]=Object.entries(r)[0];u[t]=e}else{u.fontColor=r}}if(i!==void 0&&i!==null){if(typeof i==="object"){let[t,e]=Object.entries(i)[0];u[t]=e}else{u.borderColor=i}}if(n!==void 0&&n!==null){if(typeof n==="object"){let[t,e]=Object.entries(n)[0];u[t]=e}else{u.shadowing=n}}if(s!==void 0&&s!==null){if(typeof s==="object"){let[t,e]=Object.entries(s)[0];u[t]=e}else{u.shape=s}}if(l!==void 0&&l!==null){if(typeof l==="object"){let[t,e]=Object.entries(l)[0];u[t]=e}else{u.sprite=l}}if(o!==void 0&&o!==null){if(typeof o==="object"){let[t,e]=Object.entries(o)[0];u[t]=e}else{u.techn=o}}if(c!==void 0&&c!==null){if(typeof c==="object"){let[t,e]=Object.entries(c)[0];u[t]=e}else{u.legendText=c}}if(h!==void 0&&h!==null){if(typeof h==="object"){let[t,e]=Object.entries(h)[0];u[t]=e}else{u.legendSprite=h}}}),"updateElStyle");var P=(0,n.K2)((function(t,e,a,r,i,n,s){const l=b.find((t=>t.from===e&&t.to===a));if(l===void 0){return}if(r!==void 0&&r!==null){if(typeof r==="object"){let[t,e]=Object.entries(r)[0];l[t]=e}else{l.textColor=r}}if(i!==void 0&&i!==null){if(typeof i==="object"){let[t,e]=Object.entries(i)[0];l[t]=e}else{l.lineColor=i}}if(n!==void 0&&n!==null){if(typeof n==="object"){let[t,e]=Object.entries(n)[0];l[t]=parseInt(e)}else{l.offsetX=parseInt(n)}}if(s!==void 0&&s!==null){if(typeof s==="object"){let[t,e]=Object.entries(s)[0];l[t]=parseInt(e)}else{l.offsetY=parseInt(s)}}}),"updateRelStyle");var B=(0,n.K2)((function(t,e,a){let r=_;let i=m;if(typeof e==="object"){const t=Object.values(e)[0];r=parseInt(t)}else{r=parseInt(e)}if(typeof a==="object"){const t=Object.values(a)[0];i=parseInt(t)}else{i=parseInt(a)}if(r>=1){_=r}if(i>=1){m=i}}),"updateLayoutConfig");var j=(0,n.K2)((function(){return _}),"getC4ShapeInRow");var I=(0,n.K2)((function(){return m}),"getC4BoundaryInRow");var M=(0,n.K2)((function(){return p}),"getCurrentBoundaryParse");var K=(0,n.K2)((function(){return f}),"getParentBoundaryParse");var L=(0,n.K2)((function(t){if(t===void 0||t===null){return d}else{return d.filter((e=>e.parentBoundary===t))}}),"getC4ShapeArray");var Y=(0,n.K2)((function(t){return d.find((e=>e.alias===t))}),"getC4Shape");var U=(0,n.K2)((function(t){return Object.keys(L(t))}),"getC4ShapeKeys");var F=(0,n.K2)((function(t){if(t===void 0||t===null){return y}else{return y.filter((e=>e.parentBoundary===t))}}),"getBoundaries");var X=F;var z=(0,n.K2)((function(){return b}),"getRels");var W=(0,n.K2)((function(){return g}),"getTitle");var Q=(0,n.K2)((function(t){x=t}),"setWrap");var $=(0,n.K2)((function(){return x}),"autoWrap");var H=(0,n.K2)((function(){d=[];y=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}];f="";p="global";u=[""];b=[];u=[""];g="";x=false;_=4;m=2}),"clear");var q={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25};var V={FILLED:0,OPEN:1};var G={LEFTOF:0,RIGHTOF:1,OVER:2};var J=(0,n.K2)((function(t){let e=(0,n.jZ)(t,(0,n.D7)());g=e}),"setTitle");var Z={addPersonOrSystem:A,addPersonOrSystemBoundary:O,addContainer:C,addContainerBoundary:T,addComponent:w,addDeploymentNode:R,popBoundaryParseStack:D,addRel:S,updateElStyle:N,updateRelStyle:P,updateLayoutConfig:B,autoWrap:$,setWrap:Q,getC4ShapeArray:L,getC4Shape:Y,getC4ShapeKeys:U,getBoundaries:F,getBoundarys:X,getCurrentBoundaryParse:M,getParentBoundaryParse:K,getRels:z,getTitle:W,getC4Type:k,getC4ShapeInRow:j,getC4BoundaryInRow:I,setAccTitle:n.SV,getAccTitle:n.iN,getAccDescription:n.m7,setAccDescription:n.EI,getConfig:(0,n.K2)((()=>(0,n.D7)().c4),"getConfig"),clear:H,LINETYPE:q,ARROWTYPE:V,PLACEMENT:G,setTitle:J,setC4Type:E};var tt=(0,n.K2)((function(t,e){return(0,r.tk)(t,e)}),"drawRect");var et=(0,n.K2)((function(t,e,a,r,i,n){const s=t.append("image");s.attr("width",e);s.attr("height",a);s.attr("x",r);s.attr("y",i);let o=n.startsWith("data:image/png;base64")?n:(0,l.J)(n);s.attr("xlink:href",o)}),"drawImage");var at=(0,n.K2)(((t,e,a)=>{const r=t.append("g");let i=0;for(let n of e){let t=n.textColor?n.textColor:"#444444";let e=n.lineColor?n.lineColor:"#444444";let s=n.offsetX?parseInt(n.offsetX):0;let l=n.offsetY?parseInt(n.offsetY):0;let o="";if(i===0){let t=r.append("line");t.attr("x1",n.startPoint.x);t.attr("y1",n.startPoint.y);t.attr("x2",n.endPoint.x);t.attr("y2",n.endPoint.y);t.attr("stroke-width","1");t.attr("stroke",e);t.style("fill","none");if(n.type!=="rel_b"){t.attr("marker-end","url("+o+"#arrowhead)")}if(n.type==="birel"||n.type==="rel_b"){t.attr("marker-start","url("+o+"#arrowend)")}i=-1}else{let t=r.append("path");t.attr("fill","none").attr("stroke-width","1").attr("stroke",e).attr("d","Mstartx,starty Qcontrolx,controly stopx,stopy ".replaceAll("startx",n.startPoint.x).replaceAll("starty",n.startPoint.y).replaceAll("controlx",n.startPoint.x+(n.endPoint.x-n.startPoint.x)/2-(n.endPoint.x-n.startPoint.x)/4).replaceAll("controly",n.startPoint.y+(n.endPoint.y-n.startPoint.y)/2).replaceAll("stopx",n.endPoint.x).replaceAll("stopy",n.endPoint.y));if(n.type!=="rel_b"){t.attr("marker-end","url("+o+"#arrowhead)")}if(n.type==="birel"||n.type==="rel_b"){t.attr("marker-start","url("+o+"#arrowend)")}}let c=a.messageFont();ft(a)(n.label.text,r,Math.min(n.startPoint.x,n.endPoint.x)+Math.abs(n.endPoint.x-n.startPoint.x)/2+s,Math.min(n.startPoint.y,n.endPoint.y)+Math.abs(n.endPoint.y-n.startPoint.y)/2+l,n.label.width,n.label.height,{fill:t},c);if(n.techn&&n.techn.text!==""){c=a.messageFont();ft(a)("["+n.techn.text+"]",r,Math.min(n.startPoint.x,n.endPoint.x)+Math.abs(n.endPoint.x-n.startPoint.x)/2+s,Math.min(n.startPoint.y,n.endPoint.y)+Math.abs(n.endPoint.y-n.startPoint.y)/2+a.messageFontSize+5+l,Math.max(n.label.width,n.techn.width),n.techn.height,{fill:t,"font-style":"italic"},c)}}}),"drawRels");var rt=(0,n.K2)((function(t,e,a){const r=t.append("g");let i=e.bgColor?e.bgColor:"none";let n=e.borderColor?e.borderColor:"#444444";let s=e.fontColor?e.fontColor:"black";let l={"stroke-width":1,"stroke-dasharray":"7.0,7.0"};if(e.nodeType){l={"stroke-width":1}}let o={x:e.x,y:e.y,fill:i,stroke:n,width:e.width,height:e.height,rx:2.5,ry:2.5,attrs:l};tt(r,o);let c=a.boundaryFont();c.fontWeight="bold";c.fontSize=c.fontSize+2;c.fontColor=s;ft(a)(e.label.text,r,e.x,e.y+e.label.Y,e.width,e.height,{fill:"#444444"},c);if(e.type&&e.type.text!==""){c=a.boundaryFont();c.fontColor=s;ft(a)(e.type.text,r,e.x,e.y+e.type.Y,e.width,e.height,{fill:"#444444"},c)}if(e.descr&&e.descr.text!==""){c=a.boundaryFont();c.fontSize=c.fontSize-2;c.fontColor=s;ft(a)(e.descr.text,r,e.x,e.y+e.descr.Y,e.width,e.height,{fill:"#444444"},c)}}),"drawBoundary");var it=(0,n.K2)((function(t,e,a){let i=e.bgColor?e.bgColor:a[e.typeC4Shape.text+"_bg_color"];let n=e.borderColor?e.borderColor:a[e.typeC4Shape.text+"_border_color"];let s=e.fontColor?e.fontColor:"#FFFFFF";let l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";switch(e.typeC4Shape.text){case"person":l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";break;case"external_person":l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=";break}const o=t.append("g");o.attr("class","person-man");const c=(0,r.PB)();switch(e.typeC4Shape.text){case"person":case"external_person":case"system":case"external_system":case"container":case"external_container":case"component":case"external_component":c.x=e.x;c.y=e.y;c.fill=i;c.width=e.width;c.height=e.height;c.stroke=n;c.rx=2.5;c.ry=2.5;c.attrs={"stroke-width":.5};tt(o,c);break;case"system_db":case"external_system_db":case"container_db":case"external_container_db":case"component_db":case"external_component_db":o.append("path").attr("fill",i).attr("stroke-width","0.5").attr("stroke",n).attr("d","Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2).replaceAll("height",e.height));o.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",n).attr("d","Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2));break;case"system_queue":case"external_system_queue":case"container_queue":case"external_container_queue":case"component_queue":case"external_component_queue":o.append("path").attr("fill",i).attr("stroke-width","0.5").attr("stroke",n).attr("d","Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("width",e.width).replaceAll("half",e.height/2));o.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",n).attr("d","Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half".replaceAll("startx",e.x+e.width).replaceAll("starty",e.y).replaceAll("half",e.height/2));break}let h=pt(a,e.typeC4Shape.text);o.append("text").attr("fill",s).attr("font-family",h.fontFamily).attr("font-size",h.fontSize-2).attr("font-style","italic").attr("lengthAdjust","spacing").attr("textLength",e.typeC4Shape.width).attr("x",e.x+e.width/2-e.typeC4Shape.width/2).attr("y",e.y+e.typeC4Shape.Y).text("<<"+e.typeC4Shape.text+">>");switch(e.typeC4Shape.text){case"person":case"external_person":et(o,48,48,e.x+e.width/2-24,e.y+e.image.Y,l);break}let d=a[e.typeC4Shape.text+"Font"]();d.fontWeight="bold";d.fontSize=d.fontSize+2;d.fontColor=s;ft(a)(e.label.text,o,e.x,e.y+e.label.Y,e.width,e.height,{fill:s},d);d=a[e.typeC4Shape.text+"Font"]();d.fontColor=s;if(e.techn&&e.techn?.text!==""){ft(a)(e.techn.text,o,e.x,e.y+e.techn.Y,e.width,e.height,{fill:s,"font-style":"italic"},d)}else if(e.type&&e.type.text!==""){ft(a)(e.type.text,o,e.x,e.y+e.type.Y,e.width,e.height,{fill:s,"font-style":"italic"},d)}if(e.descr&&e.descr.text!==""){d=a.personFont();d.fontColor=s;ft(a)(e.descr.text,o,e.x,e.y+e.descr.Y,e.width,e.height,{fill:s},d)}return e.height}),"drawC4Shape");var nt=(0,n.K2)((function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")}),"insertDatabaseIcon");var st=(0,n.K2)((function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")}),"insertComputerIcon");var lt=(0,n.K2)((function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")}),"insertClockIcon");var ot=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")}),"insertArrowHead");var ct=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","arrowend").attr("refX",1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 10 0 L 0 5 L 10 10 z")}),"insertArrowEnd");var ht=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",18).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")}),"insertArrowFilledHead");var dt=(0,n.K2)((function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)}),"insertDynamicNumber");var ut=(0,n.K2)((function(t){const e=t.append("defs");const a=e.append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",16).attr("refY",4);a.append("path").attr("fill","black").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 9,2 V 6 L16,4 Z");a.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 0,1 L 6,7 M 6,1 L 0,7")}),"insertArrowCrossHead");var pt=(0,n.K2)(((t,e)=>({fontFamily:t[e+"FontFamily"],fontSize:t[e+"FontSize"],fontWeight:t[e+"FontWeight"]})),"getC4ShapeFont");var ft=function(){function t(t,e,a,i,n,s,l){const o=e.append("text").attr("x",a+n/2).attr("y",i+s/2+5).style("text-anchor","middle").text(t);r(o,l)}(0,n.K2)(t,"byText");function e(t,e,a,i,s,l,o,c){const{fontSize:h,fontFamily:d,fontWeight:u}=c;const p=t.split(n.Y2.lineBreakRegex);for(let n=0;n<p.length;n++){const t=n*h-h*(p.length-1)/2;const l=e.append("text").attr("x",a+s/2).attr("y",i).style("text-anchor","middle").attr("dominant-baseline","middle").style("font-size",h).style("font-weight",u).style("font-family",d);l.append("tspan").attr("dy",t).text(p[n]).attr("alignment-baseline","mathematical");r(l,o)}}(0,n.K2)(e,"byTspan");function a(t,a,i,n,s,l,o,c){const h=a.append("switch");const d=h.append("foreignObject").attr("x",i).attr("y",n).attr("width",s).attr("height",l);const u=d.append("xhtml:div").style("display","table").style("height","100%").style("width","100%");u.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t);e(t,h,i,n,s,l,o,c);r(u,o)}(0,n.K2)(a,"byFo");function r(t,e){for(const a in e){if(e.hasOwnProperty(a)){t.attr(a,e[a])}}}(0,n.K2)(r,"_setTextAttrs");return function(r){return r.textPlacement==="fo"?a:r.textPlacement==="old"?t:e}}();var yt={drawRect:tt,drawBoundary:rt,drawC4Shape:it,drawRels:at,drawImage:et,insertArrowHead:ot,insertArrowEnd:ct,insertArrowFilledHead:ht,insertDynamicNumber:dt,insertArrowCrossHead:ut,insertDatabaseIcon:nt,insertComputerIcon:st,insertClockIcon:lt};var bt=0;var gt=0;var xt=4;var _t=2;o.yy=Z;var mt={};var vt=class{static{(0,n.K2)(this,"Bounds")}constructor(t){this.name="";this.data={};this.data.startx=void 0;this.data.stopx=void 0;this.data.starty=void 0;this.data.stopy=void 0;this.data.widthLimit=void 0;this.nextData={};this.nextData.startx=void 0;this.nextData.stopx=void 0;this.nextData.starty=void 0;this.nextData.stopy=void 0;this.nextData.cnt=0;kt(t.db.getConfig())}setData(t,e,a,r){this.nextData.startx=this.data.startx=t;this.nextData.stopx=this.data.stopx=e;this.nextData.starty=this.data.starty=a;this.nextData.stopy=this.data.stopy=r}updateVal(t,e,a,r){if(t[e]===void 0){t[e]=a}else{t[e]=r(a,t[e])}}insert(t){this.nextData.cnt=this.nextData.cnt+1;let e=this.nextData.startx===this.nextData.stopx?this.nextData.stopx+t.margin:this.nextData.stopx+t.margin*2;let a=e+t.width;let r=this.nextData.starty+t.margin*2;let i=r+t.height;if(e>=this.data.widthLimit||a>=this.data.widthLimit||this.nextData.cnt>xt){e=this.nextData.startx+t.margin+mt.nextLinePaddingX;r=this.nextData.stopy+t.margin*2;this.nextData.stopx=a=e+t.width;this.nextData.starty=this.nextData.stopy;this.nextData.stopy=i=r+t.height;this.nextData.cnt=1}t.x=e;t.y=r;this.updateVal(this.data,"startx",e,Math.min);this.updateVal(this.data,"starty",r,Math.min);this.updateVal(this.data,"stopx",a,Math.max);this.updateVal(this.data,"stopy",i,Math.max);this.updateVal(this.nextData,"startx",e,Math.min);this.updateVal(this.nextData,"starty",r,Math.min);this.updateVal(this.nextData,"stopx",a,Math.max);this.updateVal(this.nextData,"stopy",i,Math.max)}init(t){this.name="";this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,widthLimit:void 0};this.nextData={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,cnt:0};kt(t.db.getConfig())}bumpLastMargin(t){this.data.stopx+=t;this.data.stopy+=t}};var kt=(0,n.K2)((function(t){(0,n.hH)(mt,t);if(t.fontFamily){mt.personFontFamily=mt.systemFontFamily=mt.messageFontFamily=t.fontFamily}if(t.fontSize){mt.personFontSize=mt.systemFontSize=mt.messageFontSize=t.fontSize}if(t.fontWeight){mt.personFontWeight=mt.systemFontWeight=mt.messageFontWeight=t.fontWeight}}),"setConf");var Et=(0,n.K2)(((t,e)=>({fontFamily:t[e+"FontFamily"],fontSize:t[e+"FontSize"],fontWeight:t[e+"FontWeight"]})),"c4ShapeFont");var St=(0,n.K2)((t=>({fontFamily:t.boundaryFontFamily,fontSize:t.boundaryFontSize,fontWeight:t.boundaryFontWeight})),"boundaryFont");var At=(0,n.K2)((t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight})),"messageFont");function Ct(t,e,a,r,s){if(!e[t].width){if(a){e[t].text=(0,i.bH)(e[t].text,s,r);e[t].textLines=e[t].text.split(n.Y2.lineBreakRegex).length;e[t].width=s;e[t].height=(0,i.ru)(e[t].text,r)}else{let a=e[t].text.split(n.Y2.lineBreakRegex);e[t].textLines=a.length;let s=0;e[t].height=0;e[t].width=0;for(const n of a){e[t].width=Math.max((0,i.Un)(n,r),e[t].width);s=(0,i.ru)(n,r);e[t].height=e[t].height+s}}}}(0,n.K2)(Ct,"calcC4ShapeTextWH");var wt=(0,n.K2)((function(t,e,a){e.x=a.data.startx;e.y=a.data.starty;e.width=a.data.stopx-a.data.startx;e.height=a.data.stopy-a.data.starty;e.label.y=mt.c4ShapeMargin-35;let r=e.wrap&&mt.wrap;let n=St(mt);n.fontSize=n.fontSize+2;n.fontWeight="bold";let s=(0,i.Un)(e.label.text,n);Ct("label",e,r,n,s);yt.drawBoundary(t,e,mt)}),"drawBoundary");var Ot=(0,n.K2)((function(t,e,a,r){let n=0;for(const s of r){n=0;const r=a[s];let l=Et(mt,r.typeC4Shape.text);l.fontSize=l.fontSize-2;r.typeC4Shape.width=(0,i.Un)("«"+r.typeC4Shape.text+"»",l);r.typeC4Shape.height=l.fontSize+2;r.typeC4Shape.Y=mt.c4ShapePadding;n=r.typeC4Shape.Y+r.typeC4Shape.height-4;r.image={width:0,height:0,Y:0};switch(r.typeC4Shape.text){case"person":case"external_person":r.image.width=48;r.image.height=48;r.image.Y=n;n=r.image.Y+r.image.height;break}if(r.sprite){r.image.width=48;r.image.height=48;r.image.Y=n;n=r.image.Y+r.image.height}let o=r.wrap&&mt.wrap;let c=mt.width-mt.c4ShapePadding*2;let h=Et(mt,r.typeC4Shape.text);h.fontSize=h.fontSize+2;h.fontWeight="bold";Ct("label",r,o,h,c);r.label.Y=n+8;n=r.label.Y+r.label.height;if(r.type&&r.type.text!==""){r.type.text="["+r.type.text+"]";let t=Et(mt,r.typeC4Shape.text);Ct("type",r,o,t,c);r.type.Y=n+5;n=r.type.Y+r.type.height}else if(r.techn&&r.techn.text!==""){r.techn.text="["+r.techn.text+"]";let t=Et(mt,r.techn.text);Ct("techn",r,o,t,c);r.techn.Y=n+5;n=r.techn.Y+r.techn.height}let d=n;let u=r.label.width;if(r.descr&&r.descr.text!==""){let t=Et(mt,r.typeC4Shape.text);Ct("descr",r,o,t,c);r.descr.Y=n+20;n=r.descr.Y+r.descr.height;u=Math.max(r.label.width,r.descr.width);d=n-r.descr.textLines*5}u=u+mt.c4ShapePadding;r.width=Math.max(r.width||mt.width,u,mt.width);r.height=Math.max(r.height||mt.height,d,mt.height);r.margin=r.margin||mt.c4ShapeMargin;t.insert(r);yt.drawC4Shape(e,r,mt)}t.bumpLastMargin(mt.c4ShapeMargin)}),"drawC4ShapeArray");var Tt=class{static{(0,n.K2)(this,"Point")}constructor(t,e){this.x=t;this.y=e}};var Rt=(0,n.K2)((function(t,e){let a=t.x;let r=t.y;let i=e.x;let n=e.y;let s=a+t.width/2;let l=r+t.height/2;let o=Math.abs(a-i);let c=Math.abs(r-n);let h=c/o;let d=t.height/t.width;let u=null;if(r==n&&a<i){u=new Tt(a+t.width,l)}else if(r==n&&a>i){u=new Tt(a,l)}else if(a==i&&r<n){u=new Tt(s,r+t.height)}else if(a==i&&r>n){u=new Tt(s,r)}if(a>i&&r<n){if(d>=h){u=new Tt(a,l+h*t.width/2)}else{u=new Tt(s-o/c*t.height/2,r+t.height)}}else if(a<i&&r<n){if(d>=h){u=new Tt(a+t.width,l+h*t.width/2)}else{u=new Tt(s+o/c*t.height/2,r+t.height)}}else if(a<i&&r>n){if(d>=h){u=new Tt(a+t.width,l-h*t.width/2)}else{u=new Tt(s+t.height/2*o/c,r)}}else if(a>i&&r>n){if(d>=h){u=new Tt(a,l-t.width/2*h)}else{u=new Tt(s-t.height/2*o/c,r)}}return u}),"getIntersectPoint");var Dt=(0,n.K2)((function(t,e){let a={x:0,y:0};a.x=e.x+e.width/2;a.y=e.y+e.height/2;let r=Rt(t,a);a.x=t.x+t.width/2;a.y=t.y+t.height/2;let i=Rt(e,a);return{startPoint:r,endPoint:i}}),"getIntersectPoints");var Nt=(0,n.K2)((function(t,e,a,r){let n=0;for(let s of e){n=n+1;let t=s.wrap&&mt.wrap;let e=At(mt);let l=r.db.getC4Type();if(l==="C4Dynamic"){s.label.text=n+": "+s.label.text}let o=(0,i.Un)(s.label.text,e);Ct("label",s,t,e,o);if(s.techn&&s.techn.text!==""){o=(0,i.Un)(s.techn.text,e);Ct("techn",s,t,e,o)}if(s.descr&&s.descr.text!==""){o=(0,i.Un)(s.descr.text,e);Ct("descr",s,t,e,o)}let c=a(s.from);let h=a(s.to);let d=Dt(c,h);s.startPoint=d.startPoint;s.endPoint=d.endPoint}yt.drawRels(t,e,mt)}),"drawRels");function Pt(t,e,a,r,i){let n=new vt(i);n.data.widthLimit=a.data.widthLimit/Math.min(_t,r.length);for(let[s,l]of r.entries()){let r=0;l.image={width:0,height:0,Y:0};if(l.sprite){l.image.width=48;l.image.height=48;l.image.Y=r;r=l.image.Y+l.image.height}let o=l.wrap&&mt.wrap;let c=St(mt);c.fontSize=c.fontSize+2;c.fontWeight="bold";Ct("label",l,o,c,n.data.widthLimit);l.label.Y=r+8;r=l.label.Y+l.label.height;if(l.type&&l.type.text!==""){l.type.text="["+l.type.text+"]";let t=St(mt);Ct("type",l,o,t,n.data.widthLimit);l.type.Y=r+5;r=l.type.Y+l.type.height}if(l.descr&&l.descr.text!==""){let t=St(mt);t.fontSize=t.fontSize-2;Ct("descr",l,o,t,n.data.widthLimit);l.descr.Y=r+20;r=l.descr.Y+l.descr.height}if(s==0||s%_t===0){let t=a.data.startx+mt.diagramMarginX;let e=a.data.stopy+mt.diagramMarginY+r;n.setData(t,t,e,e)}else{let t=n.data.stopx!==n.data.startx?n.data.stopx+mt.diagramMarginX:n.data.startx;let e=n.data.starty;n.setData(t,t,e,e)}n.name=l.alias;let h=i.db.getC4ShapeArray(l.alias);let d=i.db.getC4ShapeKeys(l.alias);if(d.length>0){Ot(n,t,h,d)}e=l.alias;let u=i.db.getBoundarys(e);if(u.length>0){Pt(t,e,n,u,i)}if(l.alias!=="global"){wt(t,l,n)}a.data.stopy=Math.max(n.data.stopy+mt.c4ShapeMargin,a.data.stopy);a.data.stopx=Math.max(n.data.stopx+mt.c4ShapeMargin,a.data.stopx);bt=Math.max(bt,a.data.stopx);gt=Math.max(gt,a.data.stopy)}}(0,n.K2)(Pt,"drawInsideBoundary");var Bt=(0,n.K2)((function(t,e,a,r){mt=(0,n.D7)().c4;const i=(0,n.D7)().securityLevel;let l;if(i==="sandbox"){l=(0,s.Ltv)("#i"+e)}const o=i==="sandbox"?(0,s.Ltv)(l.nodes()[0].contentDocument.body):(0,s.Ltv)("body");let c=r.db;r.db.setWrap(mt.wrap);xt=c.getC4ShapeInRow();_t=c.getC4BoundaryInRow();n.Rm.debug(`C:${JSON.stringify(mt,null,2)}`);const h=i==="sandbox"?o.select(`[id="${e}"]`):(0,s.Ltv)(`[id="${e}"]`);yt.insertComputerIcon(h);yt.insertDatabaseIcon(h);yt.insertClockIcon(h);let d=new vt(r);d.setData(mt.diagramMarginX,mt.diagramMarginX,mt.diagramMarginY,mt.diagramMarginY);d.data.widthLimit=screen.availWidth;bt=mt.diagramMarginX;gt=mt.diagramMarginY;const u=r.db.getTitle();let p=r.db.getBoundarys("");Pt(h,"",d,p,r);yt.insertArrowHead(h);yt.insertArrowEnd(h);yt.insertArrowCrossHead(h);yt.insertArrowFilledHead(h);Nt(h,r.db.getRels(),r.db.getC4Shape,r);d.data.stopx=bt;d.data.stopy=gt;const f=d.data;let y=f.stopy-f.starty;let b=y+2*mt.diagramMarginY;let g=f.stopx-f.startx;const x=g+2*mt.diagramMarginX;if(u){h.append("text").text(u).attr("x",(f.stopx-f.startx)/2-4*mt.diagramMarginX).attr("y",f.starty+mt.diagramMarginY)}(0,n.a$)(h,b,x,mt.useMaxWidth);const _=u?60:0;h.attr("viewBox",f.startx-mt.diagramMarginX+" -"+(mt.diagramMarginY+_)+" "+x+" "+(b+_));n.Rm.debug(`models:`,f)}),"draw");var jt={drawPersonOrSystemArray:Ot,drawBoundary:wt,setConf:kt,draw:Bt};var It=(0,n.K2)((t=>`.person {\n    stroke: ${t.personBorder};\n    fill: ${t.personBkg};\n  }\n`),"getStyles");var Mt=It;var Kt={parser:h,db:Z,renderer:jt,styles:Mt,init:(0,n.K2)((({c4:t,wrap:e})=>{jt.setConf(t);Z.setWrap(e)}),"init")}},60148:(t,e,a)=>{a.d(e,{CP:()=>c,HT:()=>d,PB:()=>h,aC:()=>o,lC:()=>s,m:()=>l,tk:()=>n});var r=a(75905);var i=a(16750);var n=(0,r.K2)(((t,e)=>{const a=t.append("rect");a.attr("x",e.x);a.attr("y",e.y);a.attr("fill",e.fill);a.attr("stroke",e.stroke);a.attr("width",e.width);a.attr("height",e.height);if(e.name){a.attr("name",e.name)}if(e.rx){a.attr("rx",e.rx)}if(e.ry){a.attr("ry",e.ry)}if(e.attrs!==void 0){for(const t in e.attrs){a.attr(t,e.attrs[t])}}if(e.class){a.attr("class",e.class)}return a}),"drawRect");var s=(0,r.K2)(((t,e)=>{const a={x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"};const r=n(t,a);r.lower()}),"drawBackgroundRect");var l=(0,r.K2)(((t,e)=>{const a=e.text.replace(r.H1," ");const i=t.append("text");i.attr("x",e.x);i.attr("y",e.y);i.attr("class","legend");i.style("text-anchor",e.anchor);if(e.class){i.attr("class",e.class)}const n=i.append("tspan");n.attr("x",e.x+e.textMargin*2);n.text(a);return i}),"drawText");var o=(0,r.K2)(((t,e,a,r)=>{const n=t.append("image");n.attr("x",e);n.attr("y",a);const s=(0,i.J)(r);n.attr("xlink:href",s)}),"drawImage");var c=(0,r.K2)(((t,e,a,r)=>{const n=t.append("use");n.attr("x",e);n.attr("y",a);const s=(0,i.J)(r);n.attr("xlink:href",`#${s}`)}),"drawEmbeddedImage");var h=(0,r.K2)((()=>{const t={x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0};return t}),"getNoteRect");var d=(0,r.K2)((()=>{const t={x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:true};return t}),"getTextObj")}}]);
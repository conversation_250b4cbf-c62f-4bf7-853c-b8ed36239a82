{"name": "@jupyterlab/logconsole-extension", "version": "4.4.4", "description": "JupyterLab - Log Console Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}", "schema/*.json", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -w --listEmittedFiles"}, "dependencies": {"@jupyterlab/application": "^4.4.4", "@jupyterlab/apputils": "^4.5.4", "@jupyterlab/coreutils": "^6.4.4", "@jupyterlab/docregistry": "^4.4.4", "@jupyterlab/logconsole": "^4.4.4", "@jupyterlab/rendermime": "^4.4.4", "@jupyterlab/settingregistry": "^4.4.4", "@jupyterlab/statusbar": "^4.4.4", "@jupyterlab/translation": "^4.4.4", "@jupyterlab/ui-components": "^4.4.4", "@lumino/coreutils": "^2.2.1", "@lumino/signaling": "^2.1.4", "@lumino/widgets": "^2.7.1", "react": "^18.2.0"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}
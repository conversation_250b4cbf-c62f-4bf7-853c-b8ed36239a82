{"jupyter.lab.setting-icon": "ui-components:toc", "jupyter.lab.setting-icon-label": "Table of Contents", "title": "Table of Contents", "description": "Default table of contents settings.", "jupyter.lab.menus": {"main": [{"id": "jp-mainmenu-view", "items": [{"command": "toc:show-panel", "rank": 4}]}], "context": [{"command": "toc:run-cells", "selector": ".jp-TableOfContents-content[data-document-type=\"notebook\"] .jp-tocItem"}]}, "jupyter.lab.shortcuts": [{"command": "toc:show-panel", "keys": ["Accel Shift K"], "selector": "body"}], "properties": {"maximalDepth": {"title": "Maximal headings depth", "type": "integer", "minimum": 1, "default": 4}, "numberingH1": {"title": "Enable 1st headings numbering", "description": "Whether to number first-level headings or not.", "type": "boolean", "default": true}, "numberHeaders": {"title": "Enable headings numbering", "description": "Whether to automatically number the headings or not.", "type": "boolean", "default": false}, "includeOutput": {"title": "Include cell output in headings", "description": "Whether to include cell output in headings or not.", "type": "boolean", "default": true}, "syncCollapseState": {"type": "boolean", "title": "Synchronize collapse state", "description": "If set to true, when a heading is collapsed in the table of contents the corresponding section in the document is collapsed as well and vice versa. This inhibits the cell output headings.", "default": false}, "baseNumbering": {"title": "Base level for the highest headings", "type": "integer", "description": "The number headings start at.", "default": 1}}, "additionalProperties": false, "type": "object"}
{"title": "Inline Completer", "description": "Inline completer settings.", "jupyter.lab.setting-icon": "completer:inline", "jupyter.lab.setting-icon-label": "Inline Completer", "jupyter.lab.transform": true, "jupyter.lab.shortcuts": [{"command": "inline-completer:next", "keys": ["Alt ]"], "selector": ".jp-mod-completer-enabled", "preventDefault": false}, {"command": "inline-completer:previous", "keys": ["Alt ["], "selector": ".jp-mod-completer-enabled", "preventDefault": false}, {"command": "inline-completer:accept", "keys": ["Tab"], "selector": ".jp-mod-inline-completer-active"}, {"command": "inline-completer:accept", "keys": ["Alt End"], "selector": ".jp-mod-inline-completer-active"}, {"command": "inline-completer:invoke", "keys": ["Alt \\"], "selector": ".jp-mod-completer-enabled", "preventDefault": false}], "properties": {"providers": {"title": "Inline completion providers", "type": "object", "default": {}}, "showWidget": {"title": "Show widget", "description": "When to show the inline completer widget.", "type": "string", "oneOf": [{"const": "always", "title": "Always"}, {"const": "onHover", "title": "On hover"}, {"const": "never", "title": "Never"}], "default": "onHover"}, "showShortcuts": {"title": "Show shortcuts in the widget", "description": "Whether to show shortcuts in the inline completer widget.", "type": "boolean", "default": true}, "suppressIfTabCompleterActive": {"title": "Suppress when the tab completer is active", "description": "Whether to suppress the inline completer when the tab completer suggestions are shown.", "type": "boolean", "default": true}, "streamingAnimation": {"title": "Streaming animation", "description": "Transition effect used when streaming tokens from model.", "type": "string", "oneOf": [{"const": "none", "title": "None"}, {"const": "uncover", "title": "Uncover"}], "default": "uncover"}, "minLines": {"title": "Reserve lines for inline completion", "description": "Number of lines to reserve for the ghost text with inline completion suggestion.", "type": "number", "default": 0, "minimum": 0}, "maxLines": {"title": "Limit inline completion lines", "description": "Number of lines of inline completion to show before collapsing. Setting zero disables the limit.", "type": "number", "default": 0, "minimum": 0}, "reserveSpaceForLongest": {"title": "Reserve space for the longest candidate", "description": "When multiple completions are returned, reserve blank space for up to as many lines as in the longest completion candidate to avoid resizing editor when cycling between the suggestions.", "type": "boolean", "default": false}, "editorResizeDelay": {"title": "Editor resize delay", "description": "When an inline completion gets cancelled the editor may change its size rapidly. When typing in the editor, the completions may get dismissed frequently causing a noticeable jitter of the editor height. Adding a delay prevents the jitter on typing. The value should be in milliseconds.", "type": "number", "default": 1000, "minimum": 0}}, "additionalProperties": false, "type": "object"}
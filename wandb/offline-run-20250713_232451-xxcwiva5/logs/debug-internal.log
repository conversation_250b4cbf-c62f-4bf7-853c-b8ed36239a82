{"time":"2025-07-13T23:24:52.162814+01:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-13T23:24:52.174615+01:00","level":"ERROR","msg":"monitor: failed to initialize GPU resource: monitor: could not start GPU binary: fork/exec /Users/<USER>/Desktop/CERN_ML_Project/physics_informed_ml/physics_env/lib/python3.12/site-packages/wandb/bin/gpu_stats: bad CPU type in executable"}
{"time":"2025-07-13T23:24:52.175363+01:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-13T23:24:52.17557+01:00","level":"INFO","msg":"stream: created new stream","id":"xxcwiva5"}
{"time":"2025-07-13T23:24:52.175637+01:00","level":"INFO","msg":"stream: started","id":"xxcwiva5"}
{"time":"2025-07-13T23:24:52.175677+01:00","level":"INFO","msg":"handler: started","stream_id":"xxcwiva5"}
{"time":"2025-07-13T23:24:52.175659+01:00","level":"INFO","msg":"writer: Do: started","stream_id":"xxcwiva5"}
{"time":"2025-07-13T23:24:52.175719+01:00","level":"INFO","msg":"sender: started","stream_id":"xxcwiva5"}
{"time":"2025-07-13T23:24:52.184018+01:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-07-13T23:25:34.233121+01:00","level":"INFO","msg":"stream: closing","id":"xxcwiva5"}
{"time":"2025-07-13T23:25:34.233397+01:00","level":"INFO","msg":"writer: Close: closed","stream_id":"xxcwiva5"}
{"time":"2025-07-13T23:25:34.233417+01:00","level":"INFO","msg":"sender: closed","stream_id":"xxcwiva5"}
{"time":"2025-07-13T23:25:34.233393+01:00","level":"INFO","msg":"handler: closed","stream_id":"xxcwiva5"}
{"time":"2025-07-13T23:25:34.233643+01:00","level":"INFO","msg":"stream: closed","id":"xxcwiva5"}

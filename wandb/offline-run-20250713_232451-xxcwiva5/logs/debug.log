2025-07-13 23:24:51,197 INFO    MainThread:27157 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-13 23:24:51,198 INFO    MainThread:27157 [wandb_setup.py:_flush():80] Configure stats pid to 27157
2025-07-13 23:24:51,198 INFO    MainThread:27157 [wandb_setup.py:_flush():80] Loading settings from /Users/<USER>/.config/wandb/settings
2025-07-13 23:24:51,198 INFO    MainThread:27157 [wandb_setup.py:_flush():80] Loading settings from /Users/<USER>/Desktop/CERN_ML_Project/physics_informed_ml/wandb/settings
2025-07-13 23:24:51,198 INFO    MainThread:27157 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-13 23:24:51,198 INFO    MainThread:27157 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /Users/<USER>/Desktop/CERN_ML_Project/physics_informed_ml/wandb/offline-run-20250713_232451-xxcwiva5/logs/debug.log
2025-07-13 23:24:51,198 INFO    MainThread:27157 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /Users/<USER>/Desktop/CERN_ML_Project/physics_informed_ml/wandb/offline-run-20250713_232451-xxcwiva5/logs/debug-internal.log
2025-07-13 23:24:51,199 INFO    MainThread:27157 [wandb_init.py:init():830] calling init triggers
2025-07-13 23:24:51,199 INFO    MainThread:27157 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'data': {'dataset_path': 'dataset/dielectric.csv', 'batch_size': 128, 'train_split': 0.7, 'val_split': 0.15, 'test_split': 0.15, 'num_workers': 4, 'shuffle': True}, 'model': {'pinn': {'hidden_dims': [256, 512, 256, 128], 'activation': 'tanh', 'dropout_rate': 0.1, 'physics_loss_weight': 1.0}, 'gnn': {'node_features': 10, 'edge_features': 5, 'hidden_dim': 128, 'num_layers': 4, 'aggregation': 'mean'}, 'transformer': {'d_model': 256, 'nhead': 8, 'num_encoder_layers': 6, 'dim_feedforward': 1024, 'dropout': 0.1}, 'vae': {'latent_dim': 64, 'encoder_dims': [256, 128], 'decoder_dims': [128, 256], 'beta': 1.0}, 'hybrid': {'fusion_dim': 512, 'output_dim': 2, 'uncertainty_heads': 5}}, 'training': {'epochs': 100, 'learning_rate': 0.001, 'weight_decay': '1e-5', 'scheduler': 'cosine', 'early_stopping_patience': 15, 'gradient_clip_val': 1.0, 'loss_weights': {'reconstruction': 1.0, 'physics_constraint': 2.0, 'anomaly_detection': 0.5, 'uncertainty': 0.3}}, 'physics': {'energy_conservation_tolerance': '1e-3', 'momentum_conservation_tolerance': '1e-3', 'charge_conservation_tolerance': '1e-6', 'electron_mass': 0.511, 'speed_of_light': 299792458, 'elementary_charge': 1.602176634e-19}, 'inference': {'batch_size': 1, 'uncertainty_samples': 100, 'anomaly_threshold': 0.95, 'real_time_buffer_size': 1000}, 'visualization': {'plot_style': 'seaborn-v0_8', 'figure_size': [12, 8], 'dpi': 300, 'save_format': 'png', 'particle_size': 0.1, 'trajectory_alpha': 0.7, 'animation_fps': 30}, 'logging': {'level': 'INFO', 'log_dir': 'logs', 'wandb_project': 'physics-discovery', 'save_plots': True, 'plot_interval': 10}, 'hardware': {'device': 'auto', 'mixed_precision': True, 'compile_model': True}, 'paths': {'models_dir': 'models', 'plots_dir': 'plots', 'logs_dir': 'logs', 'frontend_dir': 'frontend'}, '_wandb': {}}
2025-07-13 23:24:51,199 INFO    MainThread:27157 [wandb_init.py:init():871] starting backend
2025-07-13 23:24:52,074 INFO    MainThread:27157 [wandb_init.py:init():874] sending inform_init request
2025-07-13 23:24:52,144 INFO    MainThread:27157 [wandb_init.py:init():882] backend started and connected
2025-07-13 23:24:52,151 INFO    MainThread:27157 [wandb_init.py:init():953] updated telemetry
2025-07-13 23:24:52,181 INFO    MainThread:27157 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-13 23:24:52,186 INFO    MainThread:27157 [wandb_init.py:init():1029] starting run threads in backend
2025-07-13 23:24:52,428 INFO    MainThread:27157 [wandb_run.py:_console_start():2458] atexit reg
2025-07-13 23:24:52,428 INFO    MainThread:27157 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-13 23:24:52,428 INFO    MainThread:27157 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-13 23:24:52,428 INFO    MainThread:27157 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-13 23:24:52,432 INFO    MainThread:27157 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-13 23:24:52,432 INFO    MainThread:27157 [wandb_watch.py:_watch():70] Watching
2025-07-13 23:25:34,232 INFO    MsgRouterThr:27157 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 0 handles.

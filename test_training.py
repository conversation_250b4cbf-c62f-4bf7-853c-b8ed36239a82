#!/usr/bin/env python3
"""
Simple test script to debug training issues.
"""

import sys
import os
sys.path.append('src')

import torch
import torch.nn as nn
import numpy as np
import yaml
import pickle
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all imports work correctly."""
    try:
        from models.physics_informed_nn import PhysicsInformedNN, MultiScalePINN
        logger.info("✅ Physics-informed NN imports successful")
        
        from models.graph_neural_network import PhysicsConstrainedGraphNet
        logger.info("✅ Graph neural network imports successful")
        
        from models.transformer_attention import PhysicsTransformer
        logger.info("✅ Transformer imports successful")
        
        from models.variational_autoencoder import PhysicsVAE
        logger.info("✅ VAE imports successful")
        
        from models.hybrid_model import HybridPhysicsModel
        logger.info("✅ Hybrid model imports successful")
        
        from training.physics_losses import PhysicsInformedLoss
        logger.info("✅ Physics losses imports successful")
        
        from training.trainer import PhysicsTrainer
        logger.info("✅ Trainer imports successful")
        
        from data.data_loader import PhysicsDataModule
        logger.info("✅ Data loader imports successful")
        
        return True
    except Exception as e:
        logger.error(f"❌ Import failed: {e}")
        return False

def test_model_creation():
    """Test if we can create the model."""
    try:
        # Load config
        with open('config/config.yaml', 'r') as f:
            config = yaml.safe_load(f)

        # Import the models
        from models.physics_informed_nn import PhysicsInformedNN
        from models.hybrid_model import HybridPhysicsModel

        # Create a simple test model first
        input_dim = 17
        logger.info(f"Creating model with input_dim={input_dim}")

        # Test individual components first
        pinn = PhysicsInformedNN(input_dim, config)
        logger.info("✅ PINN created successfully")
        
        # Test with dummy data
        dummy_input = torch.randn(32, input_dim)
        pinn_output = pinn(dummy_input)
        logger.info(f"✅ PINN forward pass successful, output shape: {pinn_output['predictions'].shape}")
        
        # Now test the hybrid model
        hybrid_model = HybridPhysicsModel(input_dim, config)
        logger.info("✅ Hybrid model created successfully")
        
        # Test forward pass
        hybrid_output = hybrid_model(dummy_input)
        logger.info(f"✅ Hybrid model forward pass successful, output shape: {hybrid_output['predictions'].shape}")
        
        return True, hybrid_model, config
        
    except Exception as e:
        logger.error(f"❌ Model creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None, None

def test_data_loading():
    """Test if we can load the processed data."""
    try:
        # Check if processed data exists
        if not os.path.exists('models/processed_data.pkl'):
            logger.error("❌ Processed data file not found. Run preprocessing first.")
            return False, None
        
        with open('models/processed_data.pkl', 'rb') as f:
            processed_data = pickle.load(f)
        
        logger.info("✅ Processed data loaded successfully")
        
        # Check data structure
        tabular_data = processed_data['tabular']
        logger.info(f"Training samples: {len(tabular_data['X_train'])}")
        logger.info(f"Validation samples: {len(tabular_data['X_val'])}")
        logger.info(f"Test samples: {len(tabular_data['X_test'])}")
        logger.info(f"Features: {tabular_data['X_train'].shape[1]}")
        logger.info(f"Targets: {tabular_data['y_train'].shape[1]}")
        
        return True, processed_data
        
    except Exception as e:
        logger.error(f"❌ Data loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_training_step():
    """Test a single training step."""
    try:
        # Test imports
        if not test_imports():
            return False
        
        # Test data loading
        data_success, processed_data = test_data_loading()
        if not data_success:
            return False
        
        # Test model creation
        model_success, model, config = test_model_creation()
        if not model_success:
            return False
        
        # Create data loaders
        from data.data_loader import PhysicsDataModule
        data_module = PhysicsDataModule(processed_data, config)
        train_loader, val_loader, test_loader = data_module.get_tabular_loaders()
        
        logger.info("✅ Data loaders created successfully")
        
        # Test one batch
        for batch_idx, (data, targets) in enumerate(train_loader):
            logger.info(f"Batch {batch_idx}: data shape {data.shape}, targets shape {targets.shape}")
            
            # Forward pass
            outputs = model(data)
            logger.info(f"✅ Forward pass successful, output shape: {outputs['predictions'].shape}")
            
            # Test loss computation
            from training.physics_losses import PhysicsInformedLoss
            criterion = PhysicsInformedLoss(config)
            
            loss_dict = criterion(
                predictions=outputs['predictions'],
                targets=targets,
                uncertainties=None,
                features=data
            )
            
            logger.info(f"✅ Loss computation successful: {loss_dict['total'].item():.4f}")
            
            # Only test one batch
            break
        
        logger.info("✅ Training step test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Training step test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("🧪 Starting training diagnostics...")
    
    success = test_training_step()
    
    if success:
        logger.info("🎉 All tests passed! Training should work.")
    else:
        logger.error("💥 Tests failed. Check the errors above.")
        sys.exit(1)

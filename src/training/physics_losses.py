"""
Physics-informed loss functions for enforcing conservation laws and physical constraints.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, <PERSON><PERSON>, Any
import numpy as np


class ConservationLoss(nn.Module):
    """Loss functions for enforcing conservation laws."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.physics_constants = config['physics']
        
        # Speed of light in m/s
        self.c = self.physics_constants['speed_of_light']
        self.electron_mass = self.physics_constants['electron_mass']  # MeV/c²
        
    def energy_conservation_loss(self, predictions: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Enforce energy conservation.

        Args:
            predictions: [batch_size, output_dim] - variable dimensions
            targets: [batch_size, output_dim] - target values
        """
        # For 2D case (py1, py2), skip energy conservation
        if predictions.shape[1] < 4:
            return torch.tensor(0.0, device=predictions.device)

        pred_px, pred_py, pred_pz, pred_energy = predictions[:, 0], predictions[:, 1], predictions[:, 2], predictions[:, 3]

        # Calculate momentum magnitude
        p_magnitude = torch.sqrt(pred_px**2 + pred_py**2 + pred_pz**2)

        # Relativistic energy-momentum relation: E² = (pc)² + (mc²)²
        expected_energy_squared = (p_magnitude * self.c)**2 + (self.electron_mass * self.c**2)**2
        expected_energy = torch.sqrt(expected_energy_squared)
        
        # Energy conservation violation
        energy_violation = torch.abs(pred_energy - expected_energy)
        
        # Penalize violations beyond tolerance
        tolerance = self.physics_constants['energy_conservation_tolerance']
        loss = F.relu(energy_violation - tolerance)
        
        return loss.mean()
    
    def momentum_conservation_loss(self, initial_particles: torch.Tensor, 
                                 final_particles: torch.Tensor) -> torch.Tensor:
        """
        Enforce momentum conservation in particle interactions.
        
        Args:
            initial_particles: [batch_size, n_initial, 4]
            final_particles: [batch_size, n_final, 4]
        """
        # Sum momentum components
        initial_momentum = torch.sum(initial_particles[:, :, :3], dim=1)  # [batch_size, 3]
        final_momentum = torch.sum(final_particles[:, :, :3], dim=1)  # [batch_size, 3]
        
        # Momentum difference
        momentum_diff = torch.norm(initial_momentum - final_momentum, dim=1)
        
        # Penalize violations beyond tolerance
        tolerance = self.physics_constants['momentum_conservation_tolerance']
        loss = F.relu(momentum_diff - tolerance)
        
        return loss.mean()
    
    def charge_conservation_loss(self, initial_charges: torch.Tensor, 
                               final_charges: torch.Tensor) -> torch.Tensor:
        """Enforce charge conservation."""
        initial_total = torch.sum(initial_charges, dim=1)
        final_total = torch.sum(final_charges, dim=1)
        
        charge_diff = torch.abs(initial_total - final_total)
        tolerance = self.physics_constants['charge_conservation_tolerance']
        loss = F.relu(charge_diff - tolerance)
        
        return loss.mean()


class LorentzInvarianceLoss(nn.Module):
    """Enforce Lorentz invariance in predictions."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.c = config['physics']['speed_of_light']
    
    def forward(self, predictions: torch.Tensor) -> torch.Tensor:
        """
        Enforce that particles respect relativistic constraints.

        Args:
            predictions: [batch_size, output_dim] - variable dimensions
        """
        # For 2D case, skip Lorentz invariance
        if predictions.shape[1] < 4:
            return torch.tensor(0.0, device=predictions.device)

        px, py, pz, energy = predictions[:, 0], predictions[:, 1], predictions[:, 2], predictions[:, 3]

        # Four-momentum magnitude should be invariant
        p_spatial = torch.sqrt(px**2 + py**2 + pz**2)

        # Velocity must be less than speed of light
        # v/c = pc/E, must be < 1
        velocity_ratio = (p_spatial * self.c) / (energy + 1e-8)
        velocity_violation = F.relu(velocity_ratio - 0.999)  # Allow up to 99.9% c
        
        return velocity_violation.mean()


class PhysicsRegularizationLoss(nn.Module):
    """Regularization terms based on physics principles."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
    def smoothness_loss(self, predictions: torch.Tensor) -> torch.Tensor:
        """Encourage smooth physical predictions."""
        # Compute gradients between adjacent predictions
        if predictions.size(0) > 1:
            diff = predictions[1:] - predictions[:-1]
            smoothness = torch.norm(diff, dim=1)
            return smoothness.mean()
        return torch.tensor(0.0, device=predictions.device)
    
    def symmetry_loss(self, predictions: torch.Tensor) -> torch.Tensor:
        """Enforce physical symmetries."""
        # For 2D case, skip symmetry constraints
        if predictions.shape[1] < 3:
            return torch.tensor(0.0, device=predictions.device)

        # Example: rotational symmetry in momentum space
        px, py = predictions[:, 0], predictions[:, 1]
        if predictions.shape[1] >= 3:
            pz = predictions[:, 2]
        else:
            pz = torch.zeros_like(px)
        
        # Rotational invariance: |p| should be preserved under rotations
        p_magnitude = torch.sqrt(px**2 + py**2 + pz**2)
        
        # Create random rotations and check invariance
        batch_size = predictions.size(0)
        angles = torch.rand(batch_size, device=predictions.device) * 2 * np.pi
        
        # Rotate in xy plane
        px_rot = px * torch.cos(angles) - py * torch.sin(angles)
        py_rot = px * torch.sin(angles) + py * torch.cos(angles)
        
        p_magnitude_rot = torch.sqrt(px_rot**2 + py_rot**2 + pz**2)
        
        symmetry_violation = torch.abs(p_magnitude - p_magnitude_rot)
        return symmetry_violation.mean()


class AnomalyDetectionLoss(nn.Module):
    """Loss functions for anomaly detection training."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
    def contrastive_loss(self, normal_features: torch.Tensor, 
                        anomaly_features: torch.Tensor, 
                        margin: float = 1.0) -> torch.Tensor:
        """Contrastive loss for normal vs anomalous events."""
        # Compute pairwise distances
        normal_center = normal_features.mean(dim=0, keepdim=True)
        
        # Distance of normal samples to center (should be small)
        normal_distances = torch.norm(normal_features - normal_center, dim=1)
        
        # Distance of anomaly samples to center (should be large)
        anomaly_distances = torch.norm(anomaly_features - normal_center, dim=1)
        
        # Contrastive loss
        normal_loss = normal_distances.mean()
        anomaly_loss = F.relu(margin - anomaly_distances).mean()
        
        return normal_loss + anomaly_loss
    
    def isolation_loss(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """Isolation forest-inspired loss."""
        normal_mask = labels == 0
        anomaly_mask = labels == 1
        
        if normal_mask.sum() == 0 or anomaly_mask.sum() == 0:
            return torch.tensor(0.0, device=features.device)
        
        normal_features = features[normal_mask]
        anomaly_features = features[anomaly_mask]
        
        # Normal samples should be close to each other
        normal_center = normal_features.mean(dim=0, keepdim=True)
        normal_compactness = torch.norm(normal_features - normal_center, dim=1).mean()
        
        # Anomaly samples should be far from normal center
        anomaly_separation = torch.norm(anomaly_features - normal_center, dim=1).mean()
        
        return normal_compactness - 0.1 * anomaly_separation


class UncertaintyLoss(nn.Module):
    """Loss functions for uncertainty quantification."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
    def heteroscedastic_loss(self, predictions: torch.Tensor, 
                           uncertainties: torch.Tensor, 
                           targets: torch.Tensor) -> torch.Tensor:
        """
        Heteroscedastic uncertainty loss.
        
        Args:
            predictions: [batch_size, output_dim]
            uncertainties: [batch_size, output_dim] - predicted variances
            targets: [batch_size, output_dim]
        """
        # Negative log-likelihood for Gaussian with predicted variance
        mse = (predictions - targets)**2
        loss = 0.5 * torch.log(uncertainties + 1e-8) + 0.5 * mse / (uncertainties + 1e-8)
        return loss.mean()
    
    def uncertainty_regularization(self, uncertainties: torch.Tensor) -> torch.Tensor:
        """Regularize uncertainties to avoid collapse."""
        # Encourage reasonable uncertainty magnitudes
        uncertainty_penalty = F.relu(uncertainties - 10.0).mean()  # Cap max uncertainty
        uncertainty_penalty += F.relu(0.01 - uncertainties).mean()  # Minimum uncertainty
        
        return uncertainty_penalty


class PhysicsInformedLoss(nn.Module):
    """Combined physics-informed loss function."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.loss_weights = config['training']['loss_weights']
        
        # Component losses
        self.conservation_loss = ConservationLoss(config)
        self.lorentz_loss = LorentzInvarianceLoss(config)
        self.regularization_loss = PhysicsRegularizationLoss(config)
        self.anomaly_loss = AnomalyDetectionLoss(config)
        self.uncertainty_loss = UncertaintyLoss(config)
        
    def forward(self, predictions: torch.Tensor, 
                targets: torch.Tensor,
                uncertainties: torch.Tensor = None,
                anomaly_labels: torch.Tensor = None,
                features: torch.Tensor = None,
                **kwargs) -> Dict[str, torch.Tensor]:
        """
        Compute comprehensive physics-informed loss.
        
        Args:
            predictions: Model predictions
            targets: Ground truth targets
            uncertainties: Predicted uncertainties (optional)
            anomaly_labels: Anomaly labels (optional)
            features: Feature representations (optional)
        """
        losses = {}
        
        # 1. Basic reconstruction loss
        reconstruction_loss = F.mse_loss(predictions, targets)
        losses['reconstruction'] = reconstruction_loss
        
        # 2. Physics constraint losses
        energy_loss = self.conservation_loss.energy_conservation_loss(predictions, targets)
        lorentz_loss = self.lorentz_loss(predictions)
        smoothness_loss = self.regularization_loss.smoothness_loss(predictions)
        symmetry_loss = self.regularization_loss.symmetry_loss(predictions)
        
        physics_constraint_loss = energy_loss + lorentz_loss + smoothness_loss + symmetry_loss
        losses['physics_constraint'] = physics_constraint_loss
        
        # 3. Uncertainty loss (if uncertainties provided)
        if uncertainties is not None:
            uncertainty_loss = self.uncertainty_loss.heteroscedastic_loss(
                predictions, uncertainties, targets
            )
            uncertainty_reg = self.uncertainty_loss.uncertainty_regularization(uncertainties)
            losses['uncertainty'] = uncertainty_loss + uncertainty_reg
        else:
            losses['uncertainty'] = torch.tensor(0.0, device=predictions.device)
        
        # 4. Anomaly detection loss (if labels provided)
        if anomaly_labels is not None and features is not None:
            anomaly_loss = self.anomaly_loss.isolation_loss(features, anomaly_labels)
            losses['anomaly_detection'] = anomaly_loss
        else:
            losses['anomaly_detection'] = torch.tensor(0.0, device=predictions.device)
        
        # 5. Total weighted loss
        total_loss = (
            self.loss_weights['reconstruction'] * losses['reconstruction'] +
            self.loss_weights['physics_constraint'] * losses['physics_constraint'] +
            self.loss_weights['uncertainty'] * losses['uncertainty'] +
            self.loss_weights['anomaly_detection'] * losses['anomaly_detection']
        )
        
        losses['total'] = total_loss
        
        return losses
    
    def update_loss_weights(self, epoch: int, total_epochs: int):
        """Dynamically adjust loss weights during training."""
        # Gradually increase physics constraint weight
        physics_ramp = min(1.0, epoch / (total_epochs * 0.3))
        self.loss_weights['physics_constraint'] = 2.0 * physics_ramp
        
        # Reduce reconstruction weight later in training
        if epoch > total_epochs * 0.7:
            self.loss_weights['reconstruction'] = 0.5
        
        # Increase uncertainty weight in later stages
        if epoch > total_epochs * 0.5:
            self.loss_weights['uncertainty'] = 0.5
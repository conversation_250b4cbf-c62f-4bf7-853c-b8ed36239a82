"""
Physics-informed model trainer with comprehensive monitoring and optimization.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from typing import Dict, Any, List, Tuple, Optional
import logging
import os
import time
import numpy as np
from tqdm import tqdm
try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    wandb = None
    WANDB_AVAILABLE = False
from collections import defaultdict

from training.physics_losses import PhysicsInformedLoss
from models.hybrid_model import HybridPhysicsModel
try:
    from visualization.training_plots import TrainingVisualizer
except ImportError:
    TrainingVisualizer = None
try:
    from utils.metrics import PhysicsMetrics
except ImportError:
    PhysicsMetrics = None

logger = logging.getLogger(__name__)


class EarlyStopping:
    """Early stopping utility to prevent overfitting."""
    
    def __init__(self, patience: int = 10, min_delta: float = 1e-6):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = float('inf')
        self.early_stop = False
    
    def __call__(self, val_loss: float) -> bool:
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        
        return self.early_stop


class PhysicsTrainer:
    """Comprehensive trainer for physics-informed deep learning models."""
    
    def __init__(self, model: HybridPhysicsModel, config: Dict[str, Any], 
                 train_loader: DataLoader, val_loader: DataLoader, test_loader: DataLoader):
        
        self.model = model
        self.config = config
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.test_loader = test_loader
        
        # Training configuration
        self.epochs = config['training']['epochs']
        self.learning_rate = float(config['training']['learning_rate'])
        self.weight_decay = float(config['training']['weight_decay'])
        self.early_stopping_patience = config['training']['early_stopping_patience']
        self.gradient_clip_val = float(config['training']['gradient_clip_val'])
        
        # Device setup
        self.device = self._setup_device()
        self.model = self.model.to(self.device)
        
        # Loss function
        self.criterion = PhysicsInformedLoss(config)
        
        # Optimizer and scheduler
        self.optimizer = self._setup_optimizer()
        self.scheduler = self._setup_scheduler()
        
        # Early stopping
        self.early_stopping = EarlyStopping(
            patience=self.early_stopping_patience,
            min_delta=1e-6
        )
        
        # Metrics and visualization
        self.metrics = PhysicsMetrics(config)
        self.visualizer = TrainingVisualizer(config)
        
        # Training history
        self.history = defaultdict(list)
        
        # Model saving
        self.best_model_path = os.path.join(config['paths']['models_dir'], 'best_model.pth')
        self.best_val_loss = float('inf')
        
        # Logging setup
        self._setup_logging()
        
    def _setup_device(self) -> torch.device:
        """Setup training device."""
        if self.config['hardware']['device'] == 'auto':
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            device = torch.device(self.config['hardware']['device'])
        
        logger.info(f"Using device: {device}")
        return device
    
    def _setup_optimizer(self) -> optim.Optimizer:
        """Setup optimizer."""
        optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay,
            betas=(0.9, 0.999),
            eps=1e-8
        )
        return optimizer
    
    def _setup_scheduler(self) -> optim.lr_scheduler._LRScheduler:
        """Setup learning rate scheduler."""
        scheduler_type = self.config['training']['scheduler']
        
        if scheduler_type == 'cosine':
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, 
                T_max=self.epochs,
                eta_min=self.learning_rate * 0.01
            )
        elif scheduler_type == 'plateau':
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=0.5,
                patience=10,
                verbose=True
            )
        elif scheduler_type == 'step':
            scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=30,
                gamma=0.5
            )
        else:
            scheduler = optim.lr_scheduler.ConstantLR(self.optimizer, factor=1.0)
        
        return scheduler
    
    def _setup_logging(self):
        """Setup Weights & Biases logging."""
        if WANDB_AVAILABLE and self.config['logging']['wandb_project']:
            wandb.init(
                project=self.config['logging']['wandb_project'],
                config=self.config,
                name=f"physics_discovery_{int(time.time())}"
            )
            wandb.watch(self.model, log='all', log_freq=100)
    
    def train_epoch(self, epoch: int) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()
        epoch_losses = defaultdict(list)
        
        # Update loss weights
        self.criterion.update_loss_weights(epoch, self.epochs)
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {epoch+1}/{self.epochs}')
        
        for batch_idx, (data, targets) in enumerate(progress_bar):
            data, targets = data.to(self.device), targets.to(self.device)
            
            # Zero gradients
            self.optimizer.zero_grad()
            
            # Forward pass
            try:
                # Get model predictions with uncertainty
                outputs = self.model.predict_with_uncertainty(data)
                predictions = outputs['predictions']
                uncertainties = outputs['uncertainty']
                
                # Compute physics-informed loss
                loss_dict = self.criterion(
                    predictions=predictions,
                    targets=targets,
                    uncertainties=uncertainties,
                    features=data  # Use input features for anomaly detection
                )
                
                total_loss = loss_dict['total']
                
                # Backward pass
                total_loss.backward()
                
                # Gradient clipping
                if self.gradient_clip_val > 0:
                    torch.nn.utils.clip_grad_norm_(
                        self.model.parameters(), 
                        self.gradient_clip_val
                    )
                
                # Optimizer step
                self.optimizer.step()
                
                # Record losses
                for key, value in loss_dict.items():
                    if isinstance(value, torch.Tensor):
                        epoch_losses[key].append(value.item())
                
                # Update progress bar
                progress_bar.set_postfix({
                    'Loss': f'{total_loss.item():.4f}',
                    'Physics': f'{loss_dict["physics_constraint"].item():.4f}',
                    'LR': f'{self.optimizer.param_groups[0]["lr"]:.6f}'
                })
                
                # Log to wandb
                if WANDB_AVAILABLE and batch_idx % 100 == 0 and wandb.run is not None:
                    wandb.log({
                        'batch_loss': total_loss.item(),
                        'batch_physics_loss': loss_dict['physics_constraint'].item(),
                        'learning_rate': self.optimizer.param_groups[0]['lr'],
                        'epoch': epoch
                    })
                
            except Exception as e:
                logger.error(f"Error in training batch {batch_idx}: {e}")
                continue
        
        # Compute epoch averages
        epoch_avg_losses = {key: np.mean(values) for key, values in epoch_losses.items()}
        
        return epoch_avg_losses
    
    def validate_epoch(self, epoch: int) -> Dict[str, float]:
        """Validate for one epoch."""
        self.model.eval()
        epoch_losses = defaultdict(list)
        all_predictions = []
        all_targets = []
        all_uncertainties = []
        
        with torch.no_grad():
            for data, targets in tqdm(self.val_loader, desc='Validation'):
                data, targets = data.to(self.device), targets.to(self.device)
                
                try:
                    # Forward pass
                    outputs = self.model.predict_with_uncertainty(data)
                    predictions = outputs['predictions']
                    uncertainties = outputs['uncertainty']
                    
                    # Compute loss
                    loss_dict = self.criterion(
                        predictions=predictions,
                        targets=targets,
                        uncertainties=uncertainties,
                        features=data
                    )
                    
                    # Record losses
                    for key, value in loss_dict.items():
                        if isinstance(value, torch.Tensor):
                            epoch_losses[key].append(value.item())
                    
                    # Store for metrics calculation
                    all_predictions.append(predictions.cpu())
                    all_targets.append(targets.cpu())
                    all_uncertainties.append(uncertainties.cpu())
                    
                except Exception as e:
                    logger.error(f"Error in validation batch: {e}")
                    continue
        
        # Compute epoch averages
        epoch_avg_losses = {key: np.mean(values) for key, values in epoch_losses.items()}
        
        # Compute physics metrics
        if all_predictions:
            all_predictions = torch.cat(all_predictions, dim=0)
            all_targets = torch.cat(all_targets, dim=0)
            all_uncertainties = torch.cat(all_uncertainties, dim=0)
            
            physics_metrics = self.metrics.compute_physics_metrics(
                all_predictions, all_targets, all_uncertainties
            )
            epoch_avg_losses.update(physics_metrics)
        
        return epoch_avg_losses
    
    def save_checkpoint(self, epoch: int, val_loss: float, is_best: bool = False):
        """Save model checkpoint."""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'val_loss': val_loss,
            'config': self.config,
            'history': dict(self.history)
        }
        
        # Save regular checkpoint
        checkpoint_path = os.path.join(
            self.config['paths']['models_dir'], 
            f'checkpoint_epoch_{epoch}.pth'
        )
        torch.save(checkpoint, checkpoint_path)
        
        # Save best model
        if is_best:
            torch.save(checkpoint, self.best_model_path)
            logger.info(f"New best model saved with validation loss: {val_loss:.6f}")
    
    def load_checkpoint(self, checkpoint_path: str) -> int:
        """Load model checkpoint."""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.history = defaultdict(list, checkpoint['history'])
        start_epoch = checkpoint['epoch'] + 1
        
        logger.info(f"Resumed training from epoch {start_epoch}")
        return start_epoch
    
    def train(self, resume_from_checkpoint: Optional[str] = None) -> Dict[str, List[float]]:
        """Complete training loop."""
        logger.info("Starting physics-informed model training...")
        
        # Resume from checkpoint if specified
        start_epoch = 0
        if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
            start_epoch = self.load_checkpoint(resume_from_checkpoint)
        
        # Create model directory
        os.makedirs(self.config['paths']['models_dir'], exist_ok=True)
        
        # Training loop
        for epoch in range(start_epoch, self.epochs):
            epoch_start_time = time.time()
            
            # Training phase
            train_losses = self.train_epoch(epoch)
            
            # Validation phase
            val_losses = self.validate_epoch(epoch)
            
            # Scheduler step
            if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                self.scheduler.step(val_losses['total'])
            else:
                self.scheduler.step()
            
            # Record history
            for key, value in train_losses.items():
                self.history[f'train_{key}'].append(value)
            for key, value in val_losses.items():
                self.history[f'val_{key}'].append(value)
            
            # Check for best model
            current_val_loss = val_losses['total']
            is_best = current_val_loss < self.best_val_loss
            if is_best:
                self.best_val_loss = current_val_loss
            
            # Save checkpoint
            if epoch % 10 == 0 or is_best:
                self.save_checkpoint(epoch, current_val_loss, is_best)
            
            # Early stopping
            if self.early_stopping(current_val_loss):
                logger.info(f"Early stopping triggered at epoch {epoch}")
                break
            
            # Logging
            epoch_time = time.time() - epoch_start_time
            logger.info(
                f"Epoch {epoch+1}/{self.epochs} - "
                f"Train Loss: {train_losses['total']:.6f}, "
                f"Val Loss: {val_losses['total']:.6f}, "
                f"Time: {epoch_time:.2f}s"
            )
            
            # Wandb logging
            if WANDB_AVAILABLE and wandb.run is not None:
                log_dict = {
                    'epoch': epoch,
                    'epoch_time': epoch_time,
                    **{f'train_{k}': v for k, v in train_losses.items()},
                    **{f'val_{k}': v for k, v in val_losses.items()}
                }
                wandb.log(log_dict)
            
            # Generate plots periodically
            if (epoch + 1) % self.config['logging']['plot_interval'] == 0:
                self.visualizer.plot_training_progress(self.history, epoch + 1)
        
        # Final evaluation
        logger.info("Training completed. Running final evaluation...")
        test_results = self.evaluate()
        
        # Save final training plots
        self.visualizer.plot_training_progress(self.history, self.epochs)
        self.visualizer.plot_final_results(test_results)
        
        logger.info("Training and evaluation completed successfully!")
        
        return dict(self.history)
    
    def evaluate(self) -> Dict[str, Any]:
        """Comprehensive model evaluation."""
        logger.info("Evaluating model on test set...")
        
        # Load best model
        if os.path.exists(self.best_model_path):
            checkpoint = torch.load(self.best_model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
        
        self.model.eval()
        test_losses = defaultdict(list)
        all_predictions = []
        all_targets = []
        all_uncertainties = []
        all_anomaly_scores = []
        
        with torch.no_grad():
            for data, targets in tqdm(self.test_loader, desc='Testing'):
                data, targets = data.to(self.device), targets.to(self.device)
                
                # Forward pass with uncertainty
                outputs = self.model.predict_with_uncertainty(data)
                predictions = outputs['predictions']
                uncertainties = outputs['uncertainty']
                anomaly_scores = outputs['anomaly_scores']
                
                # Compute loss
                loss_dict = self.criterion(
                    predictions=predictions,
                    targets=targets,
                    uncertainties=uncertainties,
                    features=data
                )
                
                # Record losses
                for key, value in loss_dict.items():
                    if isinstance(value, torch.Tensor):
                        test_losses[key].append(value.item())
                
                # Store results
                all_predictions.append(predictions.cpu())
                all_targets.append(targets.cpu())
                all_uncertainties.append(uncertainties.cpu())
                all_anomaly_scores.append(anomaly_scores.cpu())
        
        # Aggregate results
        test_avg_losses = {key: np.mean(values) for key, values in test_losses.items()}
        
        all_predictions = torch.cat(all_predictions, dim=0)
        all_targets = torch.cat(all_targets, dim=0)
        all_uncertainties = torch.cat(all_uncertainties, dim=0)
        all_anomaly_scores = torch.cat(all_anomaly_scores, dim=0)
        
        # Compute comprehensive metrics
        physics_metrics = self.metrics.compute_physics_metrics(
            all_predictions, all_targets, all_uncertainties
        )
        
        uncertainty_metrics = self.metrics.compute_uncertainty_metrics(
            all_predictions, all_targets, all_uncertainties
        )
        
        anomaly_metrics = self.metrics.compute_anomaly_metrics(
            all_anomaly_scores, torch.zeros_like(all_anomaly_scores)  # Placeholder labels
        )
        
        # Combine all results
        test_results = {
            'losses': test_avg_losses,
            'physics_metrics': physics_metrics,
            'uncertainty_metrics': uncertainty_metrics,
            'anomaly_metrics': anomaly_metrics,
            'predictions': all_predictions.numpy(),
            'targets': all_targets.numpy(),
            'uncertainties': all_uncertainties.numpy(),
            'anomaly_scores': all_anomaly_scores.numpy()
        }
        
        # Log final results
        logger.info("Test Results:")
        logger.info(f"  Test Loss: {test_avg_losses['total']:.6f}")
        logger.info(f"  Physics RMSE: {physics_metrics.get('rmse', 0):.6f}")
        logger.info(f"  Energy Conservation Violation: {physics_metrics.get('energy_violation', 0):.6f}")
        logger.info(f"  Uncertainty Calibration Error: {uncertainty_metrics.get('calibration_error', 0):.6f}")
        
        if WANDB_AVAILABLE and wandb.run is not None:
            wandb.log({
                'test_loss': test_avg_losses['total'],
                'test_physics_rmse': physics_metrics.get('rmse', 0),
                'test_energy_violation': physics_metrics.get('energy_violation', 0),
                'test_uncertainty_calibration': uncertainty_metrics.get('calibration_error', 0)
            })
        
        return test_results
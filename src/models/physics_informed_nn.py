"""
Physics-Informed Neural Network (PINN) implementation.
Enforces conservation laws and physical constraints in the learning process.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, <PERSON><PERSON>, Any
import numpy as np


class PhysicsConstraintLayer(nn.Module):
    """Layer that enforces physics constraints."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Physics constants
        self.c = 299792458  # speed of light (m/s)
        self.electron_mass = 0.511  # MeV/c²
        
    def forward(self, predictions: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Apply physics constraints and compute violations.

        Args:
            predictions: [batch_size, output_dim] tensor

        Returns:
            Dict with corrected predictions and constraint violations
        """
        batch_size, output_dim = predictions.shape

        # For 2D case (py1, py2), we don't have full 4-momentum, so skip physics constraints
        if output_dim == 2:
            return {
                'predictions': predictions,
                'energy_violation': torch.zeros(batch_size, device=predictions.device),
                'velocity_violation': torch.zeros(batch_size, device=predictions.device)
            }

        # For 4D case, apply full physics constraints
        if output_dim >= 4:
            # Extract momentum and energy
            px, py, pz = predictions[:, 0], predictions[:, 1], predictions[:, 2]
            energy = predictions[:, 3]

            # Ensure positive energy
            energy = F.softplus(energy)

            # Calculate momentum magnitude
            p_magnitude = torch.sqrt(px**2 + py**2 + pz**2)

            # Relativistic energy-momentum relation: E² = (pc)² + (mc²)²
            # For electrons: E² = (pc)² + (0.511)²
            expected_energy = torch.sqrt((p_magnitude * self.c)**2 + self.electron_mass**2)

            # Energy conservation violation
            energy_violation = torch.abs(energy - expected_energy)

            # Velocity constraint: v/c = pc/E < 1
            velocity_ratio = (p_magnitude * self.c) / (energy + 1e-8)  # Add small epsilon
            velocity_violation = F.relu(velocity_ratio - 0.99)  # Allow up to 99% speed of light

            # Correct predictions to satisfy constraints
            corrected_predictions = torch.stack([px, py, pz, expected_energy], dim=1)

            return {
                'predictions': corrected_predictions,
                'energy_violation': energy_violation,
                'velocity_violation': velocity_violation,
                'original_predictions': predictions
            }

        # For other dimensions, just return as-is
        return {
            'predictions': predictions,
            'energy_violation': torch.zeros(batch_size, device=predictions.device),
            'velocity_violation': torch.zeros(batch_size, device=predictions.device)
        }


class PhysicsInformedNN(nn.Module):
    """Physics-Informed Neural Network with constraint enforcement."""
    
    def __init__(self, input_dim: int, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.input_dim = input_dim
        
        # Network architecture
        pinn_config = config['model']['pinn']
        hidden_dims = pinn_config['hidden_dims']
        activation = pinn_config['activation']
        dropout_rate = pinn_config['dropout_rate']
        
        # Build network layers
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                self._get_activation(activation),
                nn.Dropout(dropout_rate),
                nn.LayerNorm(hidden_dim)  # Add layer norm for stability
            ])
            prev_dim = hidden_dim
        
        # Output layer - use config to determine output dimension
        output_dim = config.get('model', {}).get('hybrid', {}).get('output_dim', 4)
        layers.append(nn.Linear(prev_dim, output_dim))
        
        self.network = nn.Sequential(*layers)
        
        # Physics constraint layer
        self.physics_layer = PhysicsConstraintLayer(config)
        
        # Initialize weights
        self._initialize_weights()
    
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function."""
        activations = {
            'tanh': nn.Tanh(),
            'relu': nn.ReLU(),
            'leaky_relu': nn.LeakyReLU(),
            'elu': nn.ELU(),
            'swish': nn.SiLU(),
            'gelu': nn.GELU()
        }
        return activations.get(activation, nn.Tanh())
    
    def _initialize_weights(self):
        """Initialize network weights using Xavier initialization."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_normal_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass with physics constraint enforcement.
        
        Args:
            x: Input features [batch_size, input_dim]
        
        Returns:
            Dict with predictions and physics information
        """
        # Network prediction
        raw_output = self.network(x)
        
        # Apply physics constraints
        physics_result = self.physics_layer(raw_output)
        
        return physics_result
    
    def compute_physics_loss(self, physics_result: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Compute physics-based loss terms."""
        energy_loss = torch.mean(physics_result['energy_violation'])
        velocity_loss = torch.mean(physics_result['velocity_violation'])
        
        total_physics_loss = energy_loss + velocity_loss
        
        return {
            'total_physics_loss': total_physics_loss,
            'energy_conservation_loss': energy_loss,
            'velocity_constraint_loss': velocity_loss
        }


class ConservationLawsModule(nn.Module):
    """Module for enforcing conservation laws across particle interactions."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.tolerance = config['physics']
        
    def check_energy_conservation(self, initial_particles: torch.Tensor, 
                                 final_particles: torch.Tensor) -> torch.Tensor:
        """
        Check energy conservation in particle interactions.
        
        Args:
            initial_particles: [batch_size, n_initial, 4] (px, py, pz, E)
            final_particles: [batch_size, n_final, 4] (px, py, pz, E)
        
        Returns:
            Energy conservation violation
        """
        initial_energy = torch.sum(initial_particles[:, :, 3], dim=1)
        final_energy = torch.sum(final_particles[:, :, 3], dim=1)
        
        energy_diff = torch.abs(initial_energy - final_energy)
        violation = F.relu(energy_diff - self.tolerance['energy_conservation_tolerance'])
        
        return violation
    
    def check_momentum_conservation(self, initial_particles: torch.Tensor, 
                                   final_particles: torch.Tensor) -> torch.Tensor:
        """Check momentum conservation (3D)."""
        initial_momentum = torch.sum(initial_particles[:, :, :3], dim=1)  # [batch, 3]
        final_momentum = torch.sum(final_particles[:, :, :3], dim=1)  # [batch, 3]
        
        momentum_diff = torch.norm(initial_momentum - final_momentum, dim=1)
        violation = F.relu(momentum_diff - self.tolerance['momentum_conservation_tolerance'])
        
        return violation
    
    def check_charge_conservation(self, initial_charges: torch.Tensor, 
                                 final_charges: torch.Tensor) -> torch.Tensor:
        """Check charge conservation."""
        initial_total = torch.sum(initial_charges, dim=1)
        final_total = torch.sum(final_charges, dim=1)
        
        charge_diff = torch.abs(initial_total - final_total)
        violation = F.relu(charge_diff - self.tolerance['charge_conservation_tolerance'])
        
        return violation


class MultiScalePINN(nn.Module):
    """Multi-scale Physics-Informed Neural Network for different physics scales."""
    
    def __init__(self, input_dim: int, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Micro-scale network (individual particle properties)
        self.micro_net = PhysicsInformedNN(input_dim, config)
        
        # Meso-scale network (particle pair interactions)
        self.meso_net = PhysicsInformedNN(input_dim * 2, config)  # Pair features
        
        # Macro-scale network (event-level properties)
        self.macro_net = PhysicsInformedNN(input_dim * 4, config)  # Event features
        
        # Fusion layer
        fusion_dim = config['model']['hybrid']['fusion_dim']
        output_dim = config['model']['hybrid']['output_dim']
        self.fusion = nn.Sequential(
            nn.Linear(output_dim * 3, fusion_dim),  # 3 scales × output_dim each
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, output_dim)
        )
        
        # Conservation laws module
        self.conservation = ConservationLawsModule(config)
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Multi-scale forward pass."""
        batch_size = x.shape[0]
        
        # Micro-scale predictions
        micro_result = self.micro_net(x)
        
        # Create meso-scale features (particle pairs)
        # For simplicity, just concatenate with shifted version
        x_shifted = torch.roll(x, shifts=1, dims=0)
        meso_input = torch.cat([x, x_shifted], dim=1)
        meso_result = self.meso_net(meso_input)
        
        # Create macro-scale features (aggregate statistics)
        x_mean = x.mean(dim=0, keepdim=True).repeat(batch_size, 1)
        x_std = x.std(dim=0, keepdim=True).repeat(batch_size, 1)
        macro_input = torch.cat([x, x_mean, x_std, x * x_mean], dim=1)
        macro_result = self.macro_net(macro_input)
        
        # Fuse multi-scale predictions
        combined_features = torch.cat([
            micro_result['predictions'],
            meso_result['predictions'],
            macro_result['predictions']
        ], dim=1)
        
        final_predictions = self.fusion(combined_features)
        
        # Apply final physics constraints
        final_result = self.micro_net.physics_layer(final_predictions)
        
        return {
            'predictions': final_result['predictions'],
            'micro_predictions': micro_result['predictions'],
            'meso_predictions': meso_result['predictions'],
            'macro_predictions': macro_result['predictions'],
            'physics_violations': {
                'energy': final_result['energy_violation'],
                'velocity': final_result['velocity_violation']
            }
        }
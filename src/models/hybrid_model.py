"""
Hybrid Physics-Informed Deep Learning Model.
Combines PINNs, GNNs, Transformers, and VAE for comprehensive physics discovery.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, List, Tuple, Optional
import logging

from models.physics_informed_nn import MultiScalePINN
from models.graph_neural_network import PhysicsConstrainedGraphNet
from models.transformer_attention import PhysicsTransformer
from models.variational_autoencoder import PhysicsVAE

logger = logging.getLogger(__name__)


class UncertaintyQuantification(nn.Module):
    """Uncertainty quantification using ensemble and Bayesian approaches."""
    
    def __init__(self, input_dim: int, output_dim: int, num_heads: int = 5):
        super().__init__()
        self.num_heads = num_heads
        
        # Multiple prediction heads for ensemble uncertainty
        self.prediction_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_dim, input_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(input_dim // 2, output_dim)
            ) for _ in range(num_heads)
        ])
        
        # Uncertainty estimation head
        self.uncertainty_head = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(),
            nn.Linear(input_dim // 2, output_dim),
            nn.Softplus()  # Ensure positive uncertainty
        )
        
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Compute predictions with uncertainty estimates."""
        # Get predictions from all heads
        predictions = [head(x) for head in self.prediction_heads]
        predictions_tensor = torch.stack(predictions, dim=0)  # [num_heads, batch_size, output_dim]
        
        # Ensemble statistics
        mean_prediction = torch.mean(predictions_tensor, dim=0)
        epistemic_uncertainty = torch.var(predictions_tensor, dim=0)  # Epistemic uncertainty
        
        # Aleatoric uncertainty
        aleatoric_uncertainty = self.uncertainty_head(x)
        
        # Total uncertainty
        total_uncertainty = epistemic_uncertainty + aleatoric_uncertainty
        
        return {
            'predictions': mean_prediction,
            'epistemic_uncertainty': epistemic_uncertainty,
            'aleatoric_uncertainty': aleatoric_uncertainty,
            'total_uncertainty': total_uncertainty,
            'individual_predictions': predictions_tensor
        }


class ModalityFusion(nn.Module):
    """Fusion module for combining different model outputs."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        fusion_dim = config['model']['hybrid']['fusion_dim']
        output_dim = config['model']['hybrid']['output_dim']
        
        # Attention-based fusion
        self.attention_weights = nn.Sequential(
            nn.Linear(fusion_dim * 4, fusion_dim),  # 4 modalities
            nn.ReLU(),
            nn.Linear(fusion_dim, 4),
            nn.Softmax(dim=-1)
        )
        
        # Feature fusion
        self.feature_fusion = nn.Sequential(
            nn.Linear(fusion_dim * 4, fusion_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim * 2, fusion_dim),
            nn.ReLU(),
            nn.Linear(fusion_dim, output_dim)
        )
        
        # Cross-modal attention
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=fusion_dim,
            num_heads=8,
            batch_first=True
        )
        
    def forward(self, pinn_features: torch.Tensor, 
                gnn_features: torch.Tensor,
                transformer_features: torch.Tensor,
                vae_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Fuse features from different modalities."""
        
        # Stack all features
        all_features = torch.stack([
            pinn_features, 
            gnn_features, 
            transformer_features, 
            vae_features
        ], dim=1)  # [batch_size, 4, fusion_dim]
        
        # Compute attention weights
        concatenated = all_features.view(all_features.size(0), -1)
        attention_weights = self.attention_weights(concatenated)
        
        # Apply attention weights
        weighted_features = all_features * attention_weights.unsqueeze(-1)
        
        # Cross-modal attention
        attended_features, attention_map = self.cross_attention(
            weighted_features, weighted_features, weighted_features
        )
        
        # Final fusion
        fused_features = attended_features.reshape(attended_features.size(0), -1)
        output = self.feature_fusion(fused_features)
        
        return {
            'fused_output': output,
            'attention_weights': attention_weights,
            'attention_map': attention_map,
            'individual_features': {
                'pinn': pinn_features,
                'gnn': gnn_features,
                'transformer': transformer_features,
                'vae': vae_features
            }
        }


class RealTimeOptimization(nn.Module):
    """Real-time optimization for inference speed."""
    
    def __init__(self, model: nn.Module):
        super().__init__()
        self.model = model
        
        # Model compilation for faster inference
        # torch.compile is not supported on Python 3.12+
        import sys
        if hasattr(torch, 'compile') and sys.version_info < (3, 12):
            try:
                self.compiled_model = torch.compile(model, mode='reduce-overhead')
                logger.info("Model compiled successfully for faster inference")
            except Exception as e:
                logger.warning(f"Model compilation failed: {e}. Using original model.")
                self.compiled_model = model
        else:
            self.compiled_model = model
            if sys.version_info >= (3, 12):
                logger.info("torch.compile not supported on Python 3.12+, using original model")
            
        # Quantization support
        self.quantized_model = None
        
    def quantize_model(self):
        """Apply dynamic quantization for faster inference."""
        self.quantized_model = torch.quantization.quantize_dynamic(
            self.model, {nn.Linear}, dtype=torch.qint8
        )
        
    def forward(self, *args, **kwargs):
        """Forward pass with optimization."""
        if self.quantized_model is not None:
            return self.quantized_model(*args, **kwargs)
        else:
            return self.compiled_model(*args, **kwargs)


class HybridPhysicsModel(nn.Module):
    """
    Complete hybrid model combining all physics-informed components.
    
    Architecture:
    1. Physics-Informed Neural Networks (PINNs) for constraint enforcement
    2. Graph Neural Networks for particle interaction modeling
    3. Transformer for sequential relationship learning
    4. Variational Autoencoder for anomaly detection
    5. Uncertainty quantification
    6. Multi-modal fusion
    """
    
    def __init__(self, input_dim: int, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.input_dim = input_dim
        
        fusion_dim = config['model']['hybrid']['fusion_dim']
        output_dim = config['model']['hybrid']['output_dim']
        
        # Component models
        self.pinn = MultiScalePINN(input_dim, config)
        self.gnn = PhysicsConstrainedGraphNet(config)
        self.transformer = PhysicsTransformer(config)
        self.vae = PhysicsVAE(input_dim, config)
        
        # Feature projection layers (to common dimension)
        self.pinn_projection = nn.Linear(output_dim, fusion_dim)
        self.gnn_projection = nn.Linear(output_dim, fusion_dim)
        self.transformer_projection = nn.Linear(output_dim, fusion_dim)
        self.vae_projection = nn.Linear(input_dim, fusion_dim)  # VAE uses reconstruction
        
        # Fusion module
        self.fusion = ModalityFusion(config)
        
        # Uncertainty quantification
        self.uncertainty = UncertaintyQuantification(
            output_dim,  # Input is the fusion output, not fusion_dim
            output_dim,
            config['model']['hybrid']['uncertainty_heads']
        )
        
        # Real-time optimization (create without circular reference)
        self.real_time_optimizer = None  # Will be set up later if needed
        
        # Anomaly detection threshold
        self.register_buffer('anomaly_threshold', torch.tensor(2.0))
        
    def forward(self, tabular_data: torch.Tensor, 
                graph_data: Optional[Any] = None,
                return_intermediates: bool = False) -> Dict[str, torch.Tensor]:
        """
        Complete forward pass through hybrid model.
        
        Args:
            tabular_data: [batch_size, input_dim] - tabular features
            graph_data: Graph data for GNN (optional)
            return_intermediates: Whether to return intermediate results
            
        Returns:
            Dict with predictions, uncertainties, and anomaly scores
        """
        
        # 1. Physics-Informed Neural Network
        pinn_result = self.pinn(tabular_data)
        pinn_features = self.pinn_projection(pinn_result['predictions'])
        
        # 2. Graph Neural Network (if graph data available)
        if graph_data is not None:
            gnn_result = self.gnn(graph_data)
            gnn_features = self.gnn_projection(gnn_result['predictions'])
        else:
            # Use PINN features as substitute
            gnn_features = pinn_features
            gnn_result = {'predictions': pinn_result['predictions']}
        
        # 3. Transformer (reshape tabular data for sequence processing)
        batch_size = tabular_data.size(0)
        # Create artificial sequence by chunking features
        seq_length = min(self.input_dim // 10, 20)  # Reasonable sequence length
        if self.input_dim >= seq_length * 10:
            transformer_input = tabular_data[:, :seq_length*10].view(batch_size, seq_length, 10)
        else:
            # Pad if necessary
            padded_size = seq_length * 10
            padded_data = F.pad(tabular_data, (0, padded_size - self.input_dim))
            transformer_input = padded_data.view(batch_size, seq_length, 10)
            
        transformer_result = self.transformer(transformer_input)
        transformer_features = self.transformer_projection(transformer_result['predictions'])
        
        # 4. Variational Autoencoder
        vae_result = self.vae(tabular_data)
        vae_features = self.vae_projection(vae_result['reconstruction'])
        
        # 5. Multi-modal fusion
        fusion_result = self.fusion(
            pinn_features, 
            gnn_features, 
            transformer_features, 
            vae_features
        )
        
        # 6. Uncertainty quantification
        uncertainty_result = self.uncertainty(fusion_result['fused_output'])
        
        # 7. Anomaly detection
        anomaly_scores = vae_result['anomaly_score']
        is_anomaly = anomaly_scores > self.anomaly_threshold
        
        # Compile results with NaN checking and clamping
        predictions = uncertainty_result['predictions']
        total_uncertainty = uncertainty_result['total_uncertainty']

        # Check for NaN/Inf values and clamp to safe ranges
        if torch.isnan(predictions).any() or torch.isinf(predictions).any():
            predictions = torch.clamp(predictions, min=-100.0, max=100.0)
            predictions = torch.nan_to_num(predictions, nan=0.0, posinf=100.0, neginf=-100.0)

        if torch.isnan(total_uncertainty).any() or torch.isinf(total_uncertainty).any():
            total_uncertainty = torch.clamp(total_uncertainty, min=0.0, max=10.0)
            total_uncertainty = torch.nan_to_num(total_uncertainty, nan=1.0, posinf=10.0, neginf=0.0)

        results = {
            'predictions': predictions,
            'uncertainty': total_uncertainty,
            'epistemic_uncertainty': torch.nan_to_num(uncertainty_result['epistemic_uncertainty'], nan=0.5),
            'aleatoric_uncertainty': torch.nan_to_num(uncertainty_result['aleatoric_uncertainty'], nan=0.5),
            'anomaly_scores': torch.nan_to_num(anomaly_scores, nan=0.0),
            'is_anomaly': is_anomaly,
            'fusion_weights': torch.nan_to_num(fusion_result['attention_weights'], nan=0.25),
            'confidence': torch.clamp(1.0 / (1.0 + total_uncertainty.mean(dim=1)), min=0.01, max=0.99)
        }
        
        # Add intermediate results if requested
        if return_intermediates:
            results['intermediates'] = {
                'pinn': pinn_result,
                'gnn': gnn_result,
                'transformer': transformer_result,
                'vae': vae_result,
                'fusion': fusion_result,
                'uncertainty': uncertainty_result
            }
        
        return results
    
    def compute_total_loss(self, predictions: Dict[str, torch.Tensor], 
                          targets: torch.Tensor,
                          tabular_data: torch.Tensor,
                          graph_data: Optional[Any] = None) -> Dict[str, torch.Tensor]:
        """Compute comprehensive loss including all physics constraints."""
        
        # Get intermediate results
        result = self.forward(tabular_data, graph_data, return_intermediates=True)
        intermediates = result['intermediates']
        
        # Loss weights from config
        weights = self.config['training']['loss_weights']
        
        # 1. Main prediction loss
        main_loss = F.mse_loss(result['predictions'], targets)
        
        # 2. Physics constraint losses
        physics_loss = 0.0
        if 'physics_violations' in intermediates['pinn']:
            physics_loss += intermediates['pinn']['physics_violations']['energy'].mean()
            physics_loss += intermediates['pinn']['physics_violations']['velocity'].mean()
        
        if 'constraint_violations' in intermediates['gnn']:
            physics_loss += intermediates['gnn']['constraint_violations']['energy'].mean()
            physics_loss += intermediates['gnn']['constraint_violations']['momentum'].mean()
        
        # 3. VAE losses
        vae_loss = intermediates['vae']['vae_loss'].mean()
        
        # 4. Uncertainty regularization
        uncertainty_loss = result['uncertainty'].mean()
        
        # 5. Total loss
        total_loss = (
            weights['reconstruction'] * main_loss +
            weights['physics_constraint'] * physics_loss +
            weights['anomaly_detection'] * vae_loss +
            weights['uncertainty'] * uncertainty_loss
        )
        
        return {
            'total_loss': total_loss,
            'main_loss': main_loss,
            'physics_loss': physics_loss,
            'vae_loss': vae_loss,
            'uncertainty_loss': uncertainty_loss
        }
    
    def predict_with_uncertainty(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Make predictions with comprehensive uncertainty estimates."""
        with torch.no_grad():
            result = self.forward(x, return_intermediates=False)
            
            # Add confidence intervals
            std = torch.sqrt(result['uncertainty'])
            predictions = result['predictions']
            
            confidence_intervals = {
                'lower_95': predictions - 1.96 * std,
                'upper_95': predictions + 1.96 * std,
                'lower_68': predictions - std,
                'upper_68': predictions + std
            }
            
            result['confidence_intervals'] = confidence_intervals
            
            return result
    
    def detect_new_physics(self, x: torch.Tensor, threshold_std: float = 3.0) -> Dict[str, torch.Tensor]:
        """Detect potential new physics events."""
        with torch.no_grad():
            result = self.predict_with_uncertainty(x)
            
            # High anomaly score + high uncertainty = potential new physics
            anomaly_score = result['anomaly_scores']
            uncertainty = result['uncertainty'].mean(dim=1)
            
            # Normalize scores
            anomaly_norm = (anomaly_score - anomaly_score.mean()) / (anomaly_score.std() + 1e-8)
            uncertainty_norm = (uncertainty - uncertainty.mean()) / (uncertainty.std() + 1e-8)
            
            # Combined new physics score
            new_physics_score = (anomaly_norm + uncertainty_norm) / 2
            is_new_physics = new_physics_score > threshold_std
            
            return {
                'new_physics_score': new_physics_score,
                'is_new_physics': is_new_physics,
                'anomaly_contribution': anomaly_norm,
                'uncertainty_contribution': uncertainty_norm,
                **result
            }
    
    def optimize_for_inference(self):
        """Optimize model for real-time inference."""
        # Switch to evaluation mode
        self.eval()

        # Apply quantization if real_time_optimizer is available
        if self.real_time_optimizer is not None:
            self.real_time_optimizer.quantize_model()
        
        # Compile critical paths
        if hasattr(torch, 'compile'):
            self.fusion = torch.compile(self.fusion, mode='reduce-overhead')
            self.uncertainty = torch.compile(self.uncertainty, mode='reduce-overhead')
        
        logger.info("Model optimized for real-time inference")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information."""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        component_params = {
            'pinn': sum(p.numel() for p in self.pinn.parameters()),
            'gnn': sum(p.numel() for p in self.gnn.parameters()),
            'transformer': sum(p.numel() for p in self.transformer.parameters()),
            'vae': sum(p.numel() for p in self.vae.parameters()),
            'fusion': sum(p.numel() for p in self.fusion.parameters()),
            'uncertainty': sum(p.numel() for p in self.uncertainty.parameters())
        }
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'component_parameters': component_params,
            'model_size_mb': total_params * 4 / (1024 * 1024),  # Assuming float32
            'input_dim': self.input_dim,
            'config': self.config
        }
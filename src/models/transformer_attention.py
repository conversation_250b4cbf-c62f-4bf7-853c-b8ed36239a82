"""
Physics-aware Transformer for sequential particle relationships.
Implements attention mechanisms that respect physics symmetries and conservation laws.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Dict, Any, Optional, Tuple


class PhysicsAwarePositionalEncoding(nn.Module):
    """Positional encoding based on physics quantities rather than sequence position."""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        self.d_model = d_model
        
        # Standard positional encoding
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
        
        # Physics-based encoding - handle physics features (typically 4D)
        self.physics_encoder = nn.Sequential(
            nn.Linear(4, d_model // 2),  # Physics features are 4D: px, py, pz, E
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model)
        )
    
    def forward(self, x: torch.Tensor, physics_features: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            x: [seq_len, batch_size, d_model]
            physics_features: [seq_len, batch_size, 4] - physics quantities
        """
        seq_len, batch_size = x.size(0), x.size(1)
        
        # Add standard positional encoding
        x = x + self.pe[:seq_len, :]
        
        # Add physics-based encoding if available
        if physics_features is not None:
            physics_enc = self.physics_encoder(physics_features)
            x = x + physics_enc
        
        return x


class ConservationAwareAttention(nn.Module):
    """Multi-head attention that respects conservation laws."""
    
    def __init__(self, d_model: int, nhead: int, dropout: float = 0.1):
        super().__init__()
        self.d_model = d_model
        self.nhead = nhead
        self.head_dim = d_model // nhead
        assert self.head_dim * nhead == d_model
        
        # Standard attention components
        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        self.out_linear = nn.Linear(d_model, d_model)
        
        # Physics-aware attention scaling
        self.physics_scale = nn.Sequential(
            nn.Linear(d_model, nhead),
            nn.Softplus()  # Ensure positive scaling
        )
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.head_dim)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                physics_mask: Optional[torch.Tensor] = None,
                attn_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            query, key, value: [seq_len, batch_size, d_model]
            physics_mask: [batch_size, seq_len, seq_len] - physics-based attention mask
            attn_mask: Standard attention mask
        """
        seq_len, batch_size, d_model = query.size()
        
        # Linear transformations
        Q = self.q_linear(query).view(seq_len, batch_size, self.nhead, self.head_dim)
        K = self.k_linear(key).view(seq_len, batch_size, self.nhead, self.head_dim)
        V = self.v_linear(value).view(seq_len, batch_size, self.nhead, self.head_dim)
        
        # Transpose for attention computation: [batch_size, nhead, seq_len, head_dim]
        Q = Q.transpose(0, 1).transpose(1, 2)
        K = K.transpose(0, 1).transpose(1, 2)
        V = V.transpose(0, 1).transpose(1, 2)
        
        # Compute attention scores
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        # Apply physics-aware scaling
        physics_scaling = self.physics_scale(query.mean(0))  # [batch_size, nhead]
        physics_scaling = physics_scaling.unsqueeze(-1).unsqueeze(-1)  # [batch_size, nhead, 1, 1]
        scores = scores * physics_scaling
        
        # Apply masks
        if attn_mask is not None:
            scores.masked_fill_(attn_mask.unsqueeze(1).unsqueeze(1), float('-inf'))
        
        if physics_mask is not None:
            # Apply physics mask to each head
            scores.masked_fill_(physics_mask.unsqueeze(1), float('-inf'))
        
        # Softmax
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # Apply attention to values
        attn_output = torch.matmul(attn_weights, V)
        
        # Reshape and apply output linear layer
        attn_output = attn_output.transpose(1, 2).transpose(0, 1)  # [seq_len, batch_size, nhead, head_dim]
        attn_output = attn_output.contiguous().view(seq_len, batch_size, d_model)
        output = self.out_linear(attn_output)
        
        return output, attn_weights.mean(dim=1)  # Average over heads for visualization


class PhysicsTransformerBlock(nn.Module):
    """Transformer block with physics-aware components."""
    
    def __init__(self, d_model: int, nhead: int, dim_feedforward: int, dropout: float = 0.1):
        super().__init__()
        
        # Physics-aware attention
        self.self_attn = ConservationAwareAttention(d_model, nhead, dropout)
        
        # Feed forward network
        self.ffn = nn.Sequential(
            nn.Linear(d_model, dim_feedforward),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(dim_feedforward, d_model)
        )
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # Physics constraint module
        self.physics_constraint = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model),
            nn.Sigmoid()  # Gate for physics corrections
        )
    
    def forward(self, src: torch.Tensor, 
                physics_mask: Optional[torch.Tensor] = None,
                src_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Forward pass through transformer block."""
        
        # Self-attention with residual connection
        attn_output, attn_weights = self.self_attn(src, src, src, physics_mask, src_mask)
        src1 = self.norm1(src + self.dropout(attn_output))
        
        # Feed forward with residual connection
        ffn_output = self.ffn(src1)
        src2 = self.norm2(src1 + self.dropout(ffn_output))
        
        # Apply physics constraints
        physics_gate = self.physics_constraint(src2)
        constrained_output = src2 * physics_gate + src1 * (1 - physics_gate)
        
        return {
            'output': constrained_output,
            'attention_weights': attn_weights,
            'physics_gate': physics_gate
        }


class PhysicsTransformer(nn.Module):
    """Complete physics-aware transformer for particle sequence modeling."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        transformer_config = config['model']['transformer']
        self.d_model = transformer_config['d_model']
        self.nhead = transformer_config['nhead']
        self.num_layers = transformer_config['num_encoder_layers']
        self.dim_feedforward = transformer_config['dim_feedforward']
        self.dropout = transformer_config['dropout']
        
        # Input projection
        self.input_projection = nn.Linear(
            config['model']['gnn']['node_features'], 
            self.d_model
        )
        
        # Physics-aware positional encoding
        self.pos_encoder = PhysicsAwarePositionalEncoding(self.d_model)
        
        # Transformer blocks
        self.transformer_blocks = nn.ModuleList([
            PhysicsTransformerBlock(
                self.d_model, 
                self.nhead, 
                self.dim_feedforward, 
                self.dropout
            ) for _ in range(self.num_layers)
        ])
        
        # Output layers
        output_dim = config.get('model', {}).get('hybrid', {}).get('output_dim', 4)
        self.output_layers = nn.Sequential(
            nn.Linear(self.d_model, self.d_model // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.d_model // 2, output_dim)
        )
        
        # Physics mask generator
        self.physics_mask_generator = PhysicsMaskGenerator(config)
        
    def create_physics_features(self, x: torch.Tensor) -> torch.Tensor:
        """Extract physics features for positional encoding."""
        # Assume last 4 features are momentum and energy
        if x.size(-1) >= 4:
            return x[..., -4:]
        else:
            # If not enough features, create dummy physics features
            batch_size, seq_len = x.size(0), x.size(1)
            return torch.zeros(seq_len, batch_size, 4, device=x.device)
    
    def forward(self, x: torch.Tensor, src_mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Args:
            x: [batch_size, seq_len, input_dim] - particle features
            src_mask: Optional attention mask
        
        Returns:
            Dict with predictions and attention information
        """
        batch_size, seq_len, input_dim = x.shape
        
        # Transpose for transformer: [seq_len, batch_size, input_dim]
        x = x.transpose(0, 1)
        
        # Project to model dimension
        x = self.input_projection(x)
        
        # Add positional encoding
        physics_features = self.create_physics_features(x)
        x = self.pos_encoder(x, physics_features)
        
        # Generate physics-aware attention masks
        physics_masks = self.physics_mask_generator(physics_features.transpose(0, 1))
        
        # Apply transformer blocks
        attention_weights = []
        physics_gates = []
        
        for i, block in enumerate(self.transformer_blocks):
            result = block(x, physics_masks, src_mask)
            x = result['output']
            attention_weights.append(result['attention_weights'])
            physics_gates.append(result['physics_gate'])
        
        # Global pooling for sequence-level prediction
        pooled = x.mean(dim=0)  # [batch_size, d_model]
        
        # Final prediction
        predictions = self.output_layers(pooled)
        
        return {
            'predictions': predictions,
            'attention_weights': attention_weights,
            'physics_gates': physics_gates,
            'final_hidden': x.transpose(0, 1)  # [batch_size, seq_len, d_model]
        }


class PhysicsMaskGenerator(nn.Module):
    """Generate attention masks based on physics relationships."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
    def forward(self, physics_features: torch.Tensor) -> torch.Tensor:
        """
        Generate physics-based attention masks.
        
        Args:
            physics_features: [batch_size, seq_len, 4] - [px, py, pz, E]
        
        Returns:
            mask: [batch_size, seq_len, seq_len] - attention mask
        """
        batch_size, seq_len, _ = physics_features.shape
        
        # Compute pairwise physics relationships
        momentum_magnitude = torch.norm(physics_features[:, :, :3], dim=-1)  # [batch_size, seq_len]
        energy = physics_features[:, :, 3]  # [batch_size, seq_len]
        
        # Create mask based on energy and momentum similarity
        energy_diff = torch.abs(energy.unsqueeze(2) - energy.unsqueeze(1))  # [batch_size, seq_len, seq_len]
        momentum_diff = torch.abs(momentum_magnitude.unsqueeze(2) - momentum_magnitude.unsqueeze(1))
        
        # Mask particles that are too different in energy or momentum
        energy_threshold = energy.mean(dim=1, keepdim=True).unsqueeze(2) * 2.0
        momentum_threshold = momentum_magnitude.mean(dim=1, keepdim=True).unsqueeze(2) * 2.0
        
        mask = (energy_diff > energy_threshold) | (momentum_diff > momentum_threshold)
        
        return mask
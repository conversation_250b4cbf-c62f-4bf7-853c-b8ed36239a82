"""
Graph Neural Network for particle interaction modeling.
Models particle collisions as dynamic graphs with physics-aware message passing.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import MessagePassing, global_mean_pool, global_max_pool
from torch_geometric.nn import GCNConv, GATConv, SAGEConv, TransformerConv
from torch_geometric.data import Bat<PERSON>
from typing import Dict, Any, Optional
import math


class PhysicsMessagePassing(MessagePassing):
    """Custom message passing layer with physics-aware interactions."""
    
    def __init__(self, node_dim: int, edge_dim: int, hidden_dim: int):
        super().__init__(aggr='mean')  # Mean aggregation
        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim
        
        # Message generation networks
        self.message_net = nn.Sequential(
            nn.Linear(2 * node_dim + edge_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # Node update network
        self.update_net = nn.Sequential(
            nn.Linear(node_dim + hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, node_dim)
        )
        
        # Physics-aware attention
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=4, batch_first=True)
        
    def forward(self, x, edge_index, edge_attr):
        """Forward pass with physics-aware message passing."""
        return self.propagate(edge_index, x=x, edge_attr=edge_attr)
    
    def message(self, x_i, x_j, edge_attr):
        """Generate messages with physics constraints."""
        # x_i: receiving node, x_j: sending node
        
        # Concatenate node features and edge attributes
        message_input = torch.cat([x_i, x_j, edge_attr], dim=-1)
        
        # Generate message
        message = self.message_net(message_input)
        
        # Apply physics-based attention (particles closer in space/momentum get higher weights)
        # Distance-based weighting (inverse square law)
        distance = edge_attr[:, 0:1]  # First edge feature is distance
        physics_weight = 1.0 / (1.0 + distance**2)
        
        return message * physics_weight
    
    def update(self, aggr_out, x):
        """Update node representations."""
        update_input = torch.cat([x, aggr_out], dim=-1)
        return self.update_net(update_input)


class ConservationAwareGNN(nn.Module):
    """GNN that enforces conservation laws through the graph structure."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        gnn_config = config['model']['gnn']
        
        self.node_features = gnn_config['node_features']
        self.edge_features = gnn_config['edge_features']
        self.hidden_dim = gnn_config['hidden_dim']
        self.num_layers = gnn_config['num_layers']
        
        # Input projection
        self.node_proj = nn.Linear(self.node_features, self.hidden_dim)
        self.edge_proj = nn.Linear(self.edge_features, self.hidden_dim)
        
        # Graph convolution layers
        self.conv_layers = nn.ModuleList()
        for _ in range(self.num_layers):
            self.conv_layers.append(
                PhysicsMessagePassing(self.hidden_dim, self.hidden_dim, self.hidden_dim)
            )
        
        # Global pooling for graph-level predictions
        self.global_pool = global_mean_pool
        
        # Output layers
        output_dim = config.get('model', {}).get('hybrid', {}).get('output_dim', 4)
        self.output_layers = nn.Sequential(
            nn.Linear(self.hidden_dim, self.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(self.hidden_dim // 2, output_dim)
        )
        
    def forward(self, data):
        """Forward pass through the GNN."""
        x, edge_index, edge_attr = data.x, data.edge_index, data.edge_attr
        batch = getattr(data, 'batch', None)
        
        # Project to hidden dimension
        x = self.node_proj(x)
        edge_attr = self.edge_proj(edge_attr)
        
        # Apply graph convolutions with residual connections
        for i, conv in enumerate(self.conv_layers):
            x_new = conv(x, edge_index, edge_attr)
            
            # Residual connection (except first layer)
            if i > 0:
                x = x + x_new
            else:
                x = x_new
                
            x = F.relu(x)
            x = F.dropout(x, p=0.1, training=self.training)
        
        # Global pooling for graph-level predictions
        if batch is not None:
            x_global = self.global_pool(x, batch)
        else:
            x_global = x.mean(dim=0, keepdim=True)
        
        # Final predictions
        output = self.output_layers(x_global)
        
        return {
            'predictions': output,
            'node_embeddings': x,
            'graph_embedding': x_global
        }


class HierarchicalGNN(nn.Module):
    """Hierarchical GNN for multi-scale particle interactions."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Different scales of interaction
        self.local_gnn = ConservationAwareGNN(config)  # Local particle interactions
        self.global_gnn = ConservationAwareGNN(config)  # Global event structure
        
        # Cross-scale attention
        hidden_dim = config['model']['gnn']['hidden_dim']
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=8,
            batch_first=True
        )
        
        # Fusion layer
        output_dim = config.get('model', {}).get('hybrid', {}).get('output_dim', 4)
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )
        
    def create_global_graph(self, data):
        """Create a global graph with long-range connections."""
        # Add global connections between all particles beyond local neighbors
        # This captures long-range correlations in the detector
        
        num_nodes = data.x.shape[0]
        
        # Create fully connected graph (for global interactions)
        global_edge_index = []
        global_edge_attr = []
        
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                global_edge_index.extend([[i, j], [j, i]])
                
                # Global edge features (simplified)
                global_edge_attr.extend([
                    [1.0, 0.0, 0.0, 0.0, 0.0],  # Global connection marker
                    [1.0, 0.0, 0.0, 0.0, 0.0]
                ])
        
        global_data = data.clone()
        global_data.edge_index = torch.tensor(global_edge_index, dtype=torch.long).t().contiguous()
        global_data.edge_attr = torch.tensor(global_edge_attr, dtype=torch.float32)
        
        return global_data
    
    def forward(self, data):
        """Hierarchical forward pass."""
        # Local interactions
        local_result = self.local_gnn(data)
        
        # Global interactions
        global_data = self.create_global_graph(data)
        global_result = self.global_gnn(global_data)
        
        # Cross-scale attention
        local_emb = local_result['graph_embedding'].unsqueeze(0)
        global_emb = global_result['graph_embedding'].unsqueeze(0)
        
        attended_local, _ = self.cross_attention(local_emb, global_emb, global_emb)
        
        # Fusion
        combined = torch.cat([
            attended_local.squeeze(0),
            global_result['graph_embedding']
        ], dim=-1)
        
        final_output = self.fusion(combined)
        
        return {
            'predictions': final_output,
            'local_predictions': local_result['predictions'],
            'global_predictions': global_result['predictions'],
            'local_embeddings': local_result['node_embeddings'],
            'global_embeddings': global_result['node_embeddings']
        }


class PhysicsConstrainedGraphNet(nn.Module):
    """Graph network with explicit physics constraint enforcement."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Base graph network
        self.base_gnn = HierarchicalGNN(config)
        
        # Physics constraint networks - make flexible for different output dimensions
        output_dim = config.get('model', {}).get('hybrid', {}).get('output_dim', 4)
        self.output_dim = output_dim

        if output_dim >= 4:
            self.energy_constraint = nn.Sequential(
                nn.Linear(output_dim, 32),
                nn.ReLU(),
                nn.Linear(32, 1),
                nn.Sigmoid()  # Energy correction factor
            )

            self.momentum_constraint = nn.Sequential(
                nn.Linear(output_dim, 32),
                nn.ReLU(),
                nn.Linear(32, min(3, output_dim-1)),  # Momentum correction
                nn.Tanh()
            )
        else:
            # For 2D case, no physics constraints
            self.energy_constraint = None
            self.momentum_constraint = None
        
    def enforce_constraints(self, predictions):
        """Enforce physics constraints on predictions."""
        # Skip constraints for 2D case
        if self.energy_constraint is None or self.momentum_constraint is None:
            return predictions

        # Energy constraint
        energy_factor = self.energy_constraint(predictions)

        # Momentum constraint
        momentum_correction = self.momentum_constraint(predictions)

        # Apply constraints
        corrected_predictions = predictions.clone()

        # Apply momentum correction (only to available dimensions)
        momentum_dims = min(momentum_correction.shape[1], predictions.shape[1] - 1)
        corrected_predictions[:, :momentum_dims] += momentum_correction[:, :momentum_dims] * 0.1

        # Apply energy correction if we have energy dimension
        if predictions.shape[1] >= 4:
            corrected_predictions[:, 3] *= (1.0 + energy_factor.squeeze(-1) * 0.1)
            # Ensure positive energy
            corrected_predictions[:, 3] = F.softplus(corrected_predictions[:, 3])

        return corrected_predictions
    
    def forward(self, data):
        """Forward pass with constraint enforcement."""
        # Get base predictions
        result = self.base_gnn(data)
        
        # Apply physics constraints
        constrained_predictions = self.enforce_constraints(result['predictions'])
        
        # Compute constraint violations
        original_pred = result['predictions']
        energy_violation = torch.abs(constrained_predictions[:, 3] - original_pred[:, 3])
        momentum_violation = torch.norm(
            constrained_predictions[:, :3] - original_pred[:, :3], dim=1
        )
        
        return {
            'predictions': constrained_predictions,
            'unconstrained_predictions': original_pred,
            'constraint_violations': {
                'energy': energy_violation,
                'momentum': momentum_violation
            },
            **{k: v for k, v in result.items() if k != 'predictions'}
        }
"""
Comprehensive metrics for physics-informed machine learning evaluation.
"""

import numpy as np
import torch
from typing import Dict, List, Any, Tu<PERSON>, Optional
from scipy import stats
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.metrics import precision_score, recall_score, f1_score, roc_auc_score
import logging

from .physics_constants import ConservationLaws, RelativisticCalculations

logger = logging.getLogger(__name__)


class PhysicsMetrics:
    """Comprehensive physics-informed metrics."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.physics_constants = config['physics']
        
    def compute_physics_metrics(self, predictions: torch.Tensor, 
                              targets: torch.Tensor,
                              uncertainties: Optional[torch.Tensor] = None) -> Dict[str, float]:
        """
        Compute comprehensive physics-informed metrics.
        
        Args:
            predictions: Model predictions [batch_size, output_dim]
            targets: Ground truth targets [batch_size, output_dim]
            uncertainties: Prediction uncertainties [batch_size, output_dim]
        
        Returns:
            Dictionary of physics metrics
        """
        metrics = {}
        
        # Convert to numpy for calculations
        pred_np = predictions.detach().cpu().numpy()
        target_np = targets.detach().cpu().numpy()
        
        if uncertainties is not None:
            unc_np = uncertainties.detach().cpu().numpy()
        else:
            unc_np = None
        
        # 1. Basic regression metrics
        metrics.update(self._compute_regression_metrics(pred_np, target_np))
        
        # 2. Physics constraint violations
        metrics.update(self._compute_physics_violations(pred_np, target_np))
        
        # 3. Conservation law metrics
        metrics.update(self._compute_conservation_metrics(pred_np, target_np))
        
        # 4. Component-wise metrics
        metrics.update(self._compute_component_metrics(pred_np, target_np))
        
        # 5. Uncertainty metrics (if available)
        if unc_np is not None:
            metrics.update(self._compute_uncertainty_metrics(pred_np, target_np, unc_np))
        
        # 6. Physics-specific metrics
        metrics.update(self._compute_physics_specific_metrics(pred_np, target_np))
        
        return metrics
    
    def _compute_regression_metrics(self, predictions: np.ndarray, 
                                  targets: np.ndarray) -> Dict[str, float]:
        """Compute standard regression metrics."""
        metrics = {}
        
        # Flatten arrays for global metrics
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()

        # Check for NaN/Inf values and handle them
        nan_mask = np.isnan(pred_flat) | np.isnan(target_flat) | np.isinf(pred_flat) | np.isinf(target_flat)
        if nan_mask.any():
            logger.warning(f"Found {nan_mask.sum()} NaN/Inf values in predictions/targets, filtering them out")
            pred_flat = pred_flat[~nan_mask]
            target_flat = target_flat[~nan_mask]

        # If all values are NaN, return default metrics
        if len(pred_flat) == 0:
            logger.warning("All predictions/targets are NaN/Inf, returning default metrics")
            return {
                'rmse': 1.0,
                'mae': 1.0,
                'r2_score': 0.0,
                'mape': 100.0,
                'max_error': 1.0
            }

        # Root Mean Square Error
        try:
            metrics['rmse'] = np.sqrt(mean_squared_error(target_flat, pred_flat))
        except Exception as e:
            logger.warning(f"Error computing RMSE: {e}, using default value")
            metrics['rmse'] = 1.0
        
        # Mean Absolute Error
        try:
            metrics['mae'] = mean_absolute_error(target_flat, pred_flat)
        except Exception as e:
            logger.warning(f"Error computing MAE: {e}, using default value")
            metrics['mae'] = 1.0

        # R-squared
        try:
            metrics['r2_score'] = r2_score(target_flat, pred_flat)
        except Exception as e:
            logger.warning(f"Error computing R2: {e}, using default value")
            metrics['r2_score'] = 0.0
        
        # Mean Absolute Percentage Error
        non_zero_mask = target_flat != 0
        if np.any(non_zero_mask):
            mape = np.mean(np.abs((target_flat[non_zero_mask] - pred_flat[non_zero_mask]) / 
                                target_flat[non_zero_mask])) * 100
            metrics['mape'] = mape
        
        # Explained Variance Score
        metrics['explained_variance'] = 1 - np.var(target_flat - pred_flat) / np.var(target_flat)
        
        # Pearson correlation coefficient
        correlation, _ = stats.pearsonr(pred_flat, target_flat)
        metrics['pearson_correlation'] = correlation
        
        return metrics
    
    def _compute_physics_violations(self, predictions: np.ndarray, 
                                  targets: np.ndarray) -> Dict[str, float]:
        """Compute physics constraint violations."""
        metrics = {}
        
        # Assume predictions format: [px, py, pz, energy]
        if predictions.shape[1] >= 4:
            px, py, pz, energy = predictions[:, 0], predictions[:, 1], predictions[:, 2], predictions[:, 3]
            
            # Energy conservation violation
            p_magnitude = np.sqrt(px**2 + py**2 + pz**2)
            c = self.physics_constants['speed_of_light']
            electron_mass = self.physics_constants['electron_mass']
            
            expected_energy = np.sqrt((p_magnitude * c)**2 + (electron_mass * c**2)**2) / c**2
            energy_violation = np.mean(np.abs(energy - expected_energy))
            metrics['energy_violation'] = energy_violation
            
            # Velocity constraint violation (v/c < 1)
            velocity_ratio = (p_magnitude * c) / (energy + 1e-8)
            velocity_violations = np.maximum(0, velocity_ratio - 0.99)
            metrics['velocity_violation'] = np.mean(velocity_violations)
            
            # Negative energy violations
            negative_energy_count = np.sum(energy < 0)
            metrics['negative_energy_fraction'] = negative_energy_count / len(energy)
            
        return metrics
    
    def _compute_conservation_metrics(self, predictions: np.ndarray, 
                                    targets: np.ndarray) -> Dict[str, float]:
        """Compute conservation law adherence metrics."""
        metrics = {}
        
        if predictions.shape[1] >= 4:
            # Treat each prediction as a four-momentum vector
            pred_four_momenta = predictions.reshape(-1, 1, 4)  # [batch, 1, 4]
            target_four_momenta = targets.reshape(-1, 1, 4)
            
            # Energy conservation check
            energy_conservation = ConservationLaws.check_energy_conservation(
                target_four_momenta, pred_four_momenta,
                tolerance=self.physics_constants['energy_conservation_tolerance']
            )
            metrics['energy_conservation_error'] = energy_conservation['difference']
            metrics['energy_conservation_satisfied'] = float(energy_conservation['is_conserved'])
            
            # Momentum conservation check
            momentum_conservation = ConservationLaws.check_momentum_conservation(
                target_four_momenta, pred_four_momenta,
                tolerance=self.physics_constants['momentum_conservation_tolerance']
            )
            metrics['momentum_conservation_error'] = momentum_conservation['difference_magnitude']
            metrics['momentum_conservation_satisfied'] = float(momentum_conservation['is_conserved'])
        
        return metrics
    
    def _compute_component_metrics(self, predictions: np.ndarray, 
                                 targets: np.ndarray) -> Dict[str, float]:
        """Compute component-wise metrics."""
        metrics = {}
        
        component_names = ['px', 'py', 'pz', 'energy']
        
        for i, component in enumerate(component_names):
            if i < predictions.shape[1]:
                pred_comp = predictions[:, i]
                target_comp = targets[:, i]
                
                # Component RMSE
                metrics[f'{component}_rmse'] = np.sqrt(mean_squared_error(target_comp, pred_comp))
                
                # Component MAE
                metrics[f'{component}_mae'] = mean_absolute_error(target_comp, pred_comp)
                
                # Component R²
                metrics[f'{component}_r2'] = r2_score(target_comp, pred_comp)
                
                # Component relative error
                if np.mean(np.abs(target_comp)) > 0:
                    relative_error = np.mean(np.abs(pred_comp - target_comp)) / np.mean(np.abs(target_comp))
                    metrics[f'{component}_relative_error'] = relative_error
        
        return metrics
    
    def _compute_uncertainty_metrics(self, predictions: np.ndarray, 
                                   targets: np.ndarray,
                                   uncertainties: np.ndarray) -> Dict[str, float]:
        """Compute uncertainty-related metrics."""
        metrics = {}
        
        # Prediction errors
        errors = np.abs(predictions - targets)
        
        # Average uncertainty
        metrics['mean_uncertainty'] = np.mean(uncertainties)
        metrics['std_uncertainty'] = np.std(uncertainties)
        
        # Uncertainty-error correlation
        error_flat = errors.flatten()
        uncertainty_flat = uncertainties.flatten()
        
        correlation, p_value = stats.pearsonr(uncertainty_flat, error_flat)
        metrics['uncertainty_error_correlation'] = correlation
        metrics['uncertainty_error_correlation_pvalue'] = p_value
        
        # Calibration metrics
        calibration_metrics = self._compute_calibration_metrics(predictions, targets, uncertainties)
        metrics.update(calibration_metrics)
        
        # Sharpness (average uncertainty)
        metrics['uncertainty_sharpness'] = np.mean(uncertainties)
        
        return metrics
    
    def _compute_calibration_metrics(self, predictions: np.ndarray, 
                                   targets: np.ndarray,
                                   uncertainties: np.ndarray) -> Dict[str, float]:
        """Compute uncertainty calibration metrics."""
        metrics = {}
        
        # For each confidence level, check coverage
        confidence_levels = [0.68, 0.95, 0.99]
        z_scores = [1.0, 1.96, 2.58]
        
        for conf_level, z_score in zip(confidence_levels, z_scores):
            # Prediction intervals
            lower_bound = predictions - z_score * np.sqrt(uncertainties)
            upper_bound = predictions + z_score * np.sqrt(uncertainties)
            
            # Check coverage
            within_interval = (targets >= lower_bound) & (targets <= upper_bound)
            empirical_coverage = np.mean(within_interval)
            
            metrics[f'coverage_{int(conf_level*100)}'] = empirical_coverage
            
            # Calibration error
            calibration_error = abs(empirical_coverage - conf_level)
            metrics[f'calibration_error_{int(conf_level*100)}'] = calibration_error
        
        # Expected Calibration Error (ECE)
        ece = self._compute_expected_calibration_error(predictions, targets, uncertainties)
        metrics['expected_calibration_error'] = ece
        
        return metrics
    
    def _compute_expected_calibration_error(self, predictions: np.ndarray,
                                          targets: np.ndarray,
                                          uncertainties: np.ndarray,
                                          n_bins: int = 10) -> float:
        """Compute Expected Calibration Error."""
        # Flatten arrays
        pred_flat = predictions.flatten()
        target_flat = targets.flatten()
        unc_flat = uncertainties.flatten()
        
        # Create confidence scores (inverse of uncertainty)
        confidence_scores = 1.0 / (1.0 + unc_flat)
        
        # Bin by confidence
        bin_boundaries = np.linspace(0, 1, n_bins + 1)
        bin_indices = np.digitize(confidence_scores, bin_boundaries) - 1
        bin_indices = np.clip(bin_indices, 0, n_bins - 1)
        
        ece = 0.0
        total_samples = len(pred_flat)
        
        for i in range(n_bins):
            mask = bin_indices == i
            if np.sum(mask) > 0:
                bin_accuracy = np.mean(np.abs(pred_flat[mask] - target_flat[mask]) < np.std(target_flat))
                bin_confidence = np.mean(confidence_scores[mask])
                bin_size = np.sum(mask)
                
                ece += (bin_size / total_samples) * abs(bin_accuracy - bin_confidence)
        
        return ece
    
    def _compute_physics_specific_metrics(self, predictions: np.ndarray,
                                        targets: np.ndarray) -> Dict[str, float]:
        """Compute physics-specific metrics."""
        metrics = {}
        
        if predictions.shape[1] >= 4:
            # Transverse momentum accuracy
            pred_pt = np.sqrt(predictions[:, 0]**2 + predictions[:, 1]**2)
            target_pt = np.sqrt(targets[:, 0]**2 + targets[:, 1]**2)
            metrics['transverse_momentum_rmse'] = np.sqrt(mean_squared_error(target_pt, pred_pt))
            
            # Total momentum accuracy
            pred_p_total = np.sqrt(np.sum(predictions[:, :3]**2, axis=1))
            target_p_total = np.sqrt(np.sum(targets[:, :3]**2, axis=1))
            metrics['total_momentum_rmse'] = np.sqrt(mean_squared_error(target_p_total, pred_p_total))
            
            # Angular accuracy (if meaningful)
            pred_theta = np.arctan2(pred_pt, predictions[:, 2])
            target_theta = np.arctan2(target_pt, targets[:, 2])
            angular_diff = np.abs(pred_theta - target_theta)
            # Handle angle wrapping
            angular_diff = np.minimum(angular_diff, 2*np.pi - angular_diff)
            metrics['polar_angle_mae'] = np.mean(angular_diff)
            
            # Pseudorapidity (if applicable)
            pred_eta = np.arcsinh(predictions[:, 2] / (pred_pt + 1e-8))
            target_eta = np.arcsinh(targets[:, 2] / (target_pt + 1e-8))
            metrics['pseudorapidity_rmse'] = np.sqrt(mean_squared_error(target_eta, pred_eta))
        
        return metrics
    
    def compute_anomaly_metrics(self, anomaly_scores: np.ndarray,
                              true_labels: np.ndarray,
                              threshold: Optional[float] = None) -> Dict[str, float]:
        """
        Compute anomaly detection metrics.
        
        Args:
            anomaly_scores: Anomaly scores from model
            true_labels: True anomaly labels (0=normal, 1=anomaly)
            threshold: Decision threshold (if None, use median)
        
        Returns:
            Dictionary of anomaly detection metrics
        """
        metrics = {}
        
        if threshold is None:
            threshold = np.median(anomaly_scores)
        
        # Binary predictions
        binary_predictions = (anomaly_scores > threshold).astype(int)
        
        # Basic classification metrics
        if len(np.unique(true_labels)) > 1:  # Check if we have both classes
            metrics['precision'] = precision_score(true_labels, binary_predictions, zero_division=0)
            metrics['recall'] = recall_score(true_labels, binary_predictions, zero_division=0)
            metrics['f1_score'] = f1_score(true_labels, binary_predictions, zero_division=0)
            
            # ROC AUC (if we have both classes)
            try:
                metrics['roc_auc'] = roc_auc_score(true_labels, anomaly_scores)
            except ValueError:
                metrics['roc_auc'] = 0.5  # Random performance
        
        # Threshold-independent metrics
        metrics['anomaly_score_mean'] = np.mean(anomaly_scores)
        metrics['anomaly_score_std'] = np.std(anomaly_scores)
        metrics['threshold_used'] = threshold
        
        # False positive and negative rates
        if len(np.unique(true_labels)) > 1:
            tn = np.sum((binary_predictions == 0) & (true_labels == 0))
            fp = np.sum((binary_predictions == 1) & (true_labels == 0))
            fn = np.sum((binary_predictions == 0) & (true_labels == 1))
            tp = np.sum((binary_predictions == 1) & (true_labels == 1))
            
            metrics['true_positive_rate'] = tp / (tp + fn) if (tp + fn) > 0 else 0
            metrics['false_positive_rate'] = fp / (fp + tn) if (fp + tn) > 0 else 0
            metrics['true_negative_rate'] = tn / (tn + fp) if (tn + fp) > 0 else 0
            metrics['false_negative_rate'] = fn / (fn + tp) if (fn + tp) > 0 else 0
        
        return metrics
    
    def compute_model_performance_summary(self, all_metrics: Dict[str, float]) -> Dict[str, Any]:
        """Create a comprehensive performance summary."""
        summary = {
            'overall_performance': {},
            'physics_compliance': {},
            'uncertainty_quality': {},
            'anomaly_detection': {}
        }
        
        # Overall performance
        summary['overall_performance'] = {
            'rmse': all_metrics.get('rmse', 0),
            'mae': all_metrics.get('mae', 0),
            'r2_score': all_metrics.get('r2_score', 0),
            'pearson_correlation': all_metrics.get('pearson_correlation', 0)
        }
        
        # Physics compliance
        summary['physics_compliance'] = {
            'energy_violation': all_metrics.get('energy_violation', 0),
            'velocity_violation': all_metrics.get('velocity_violation', 0),
            'energy_conservation_satisfied': all_metrics.get('energy_conservation_satisfied', 0),
            'momentum_conservation_satisfied': all_metrics.get('momentum_conservation_satisfied', 0)
        }
        
        # Uncertainty quality
        summary['uncertainty_quality'] = {
            'calibration_error_95': all_metrics.get('calibration_error_95', 0),
            'uncertainty_error_correlation': all_metrics.get('uncertainty_error_correlation', 0),
            'expected_calibration_error': all_metrics.get('expected_calibration_error', 0)
        }
        
        # Anomaly detection
        summary['anomaly_detection'] = {
            'f1_score': all_metrics.get('f1_score', 0),
            'roc_auc': all_metrics.get('roc_auc', 0.5),
            'precision': all_metrics.get('precision', 0),
            'recall': all_metrics.get('recall', 0)
        }
        
        # Overall score (weighted combination)
        weights = {
            'overall_performance': 0.4,
            'physics_compliance': 0.3,
            'uncertainty_quality': 0.2,
            'anomaly_detection': 0.1
        }
        
        overall_score = 0
        for category, category_metrics in summary.items():
            if category in weights:
                # Compute category score (average of normalized metrics)
                category_score = np.mean(list(category_metrics.values()))
                overall_score += weights[category] * category_score
        
        summary['overall_score'] = overall_score
        
        return summary


class ModelComparison:
    """Utilities for comparing different models."""
    
    @staticmethod
    def compare_models(model_results: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """
        Compare multiple models based on their metrics.
        
        Args:
            model_results: Dict of {model_name: metrics_dict}
        
        Returns:
            Comparison results
        """
        comparison = {
            'ranking': {},
            'best_models': {},
            'metric_comparison': {}
        }
        
        # Get all unique metrics
        all_metrics = set()
        for metrics in model_results.values():
            all_metrics.update(metrics.keys())
        
        # Rank models for each metric
        for metric in all_metrics:
            metric_values = {}
            for model_name, metrics in model_results.items():
                if metric in metrics:
                    metric_values[model_name] = metrics[metric]
            
            if metric_values:
                # Sort by metric value (ascending for error metrics, descending for score metrics)
                is_error_metric = any(error_word in metric.lower() 
                                    for error_word in ['error', 'violation', 'rmse', 'mae'])
                
                sorted_models = sorted(metric_values.items(), 
                                     key=lambda x: x[1], 
                                     reverse=not is_error_metric)
                
                comparison['ranking'][metric] = [model[0] for model in sorted_models]
                comparison['best_models'][metric] = sorted_models[0][0]
                comparison['metric_comparison'][metric] = metric_values
        
        return comparison
    
    @staticmethod
    def statistical_significance_test(results1: np.ndarray, 
                                    results2: np.ndarray,
                                    alpha: float = 0.05) -> Dict[str, Any]:
        """
        Perform statistical significance test between two sets of results.
        
        Args:
            results1: Results from model 1
            results2: Results from model 2
            alpha: Significance level
        
        Returns:
            Statistical test results
        """
        # Paired t-test
        t_stat, p_value = stats.ttest_rel(results1, results2)
        
        # Effect size (Cohen's d)
        pooled_std = np.sqrt((np.var(results1) + np.var(results2)) / 2)
        cohens_d = (np.mean(results1) - np.mean(results2)) / pooled_std
        
        # Wilcoxon signed-rank test (non-parametric)
        wilcoxon_stat, wilcoxon_p = stats.wilcoxon(results1, results2)
        
        return {
            'ttest_statistic': t_stat,
            'ttest_pvalue': p_value,
            'is_significant': p_value < alpha,
            'cohens_d': cohens_d,
            'effect_size': 'small' if abs(cohens_d) < 0.5 else 'medium' if abs(cohens_d) < 0.8 else 'large',
            'wilcoxon_statistic': wilcoxon_stat,
            'wilcoxon_pvalue': wilcoxon_p,
            'mean_difference': np.mean(results1) - np.mean(results2),
            'confidence_level': 1 - alpha
        }